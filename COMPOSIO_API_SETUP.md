# 🚀 **Composio API Integration Setup Guide**

## **Overview**

This guide provides step-by-step instructions for setting up and configuring the enhanced Composio API integration in Atlas. The new system provides access to 20+ tools per app (vs 13 MCP tools) with improved reliability and performance.

---

## 🔧 **Environment Setup**

### **Required Environment Variables**

Add these environment variables to your `.env` file or deployment configuration:

```bash
# Composio API Integration
COMPOSIO_API_KEY=your_composio_api_key_here
COMPOSIO_ENABLE_API=true
COMPOSIO_PREFER_API=false

# Optional: Advanced Configuration
COMPOSIO_BASE_URL=https://backend.composio.dev/api/v1
COMPOSIO_MCP_BASE_URL=https://mcp.composio.dev
COMPOSIO_TIMEOUT=30
COMPOSIO_MAX_REQUESTS_PER_MINUTE=100
```

### **Environment Variable Details**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `COMPOSIO_API_KEY` | **Yes** | - | Your Composio API key for direct API access |
| `COMPOSIO_ENABLE_API` | No | `false` | Enable API integration features |
| `COMPOSIO_PREFER_API` | No | `false` | Prefer API over MCP when both available |
| `COMPOSIO_BASE_URL` | No | `https://backend.composio.dev/api/v1` | Composio API base URL |
| `COMPOSIO_MCP_BASE_URL` | No | `https://mcp.composio.dev` | MCP discovery base URL |
| `COMPOSIO_TIMEOUT` | No | `30` | Request timeout in seconds |
| `COMPOSIO_MAX_REQUESTS_PER_MINUTE` | No | `100` | Rate limiting threshold |

---

## 🔑 **Getting Your Composio API Key**

### **Step 1: Composio Account Setup**
1. Visit [Composio Dashboard](https://app.composio.dev)
2. Sign up or log in to your account
3. Navigate to **Settings** → **API Keys**

### **Step 2: Generate API Key**
1. Click **"Generate New API Key"**
2. Name your key (e.g., "Atlas Integration")
3. Copy the generated key immediately (it won't be shown again)
4. Store it securely in your environment variables

### **Step 3: Verify API Key**
```bash
# Test your API key
curl -H "x-api-key: YOUR_API_KEY" https://backend.composio.dev/api/v1/health
```

---

## 📋 **Deployment Checklist**

### **Development Environment**
- [ ] Add `COMPOSIO_API_KEY` to `.env` file
- [ ] Set `COMPOSIO_ENABLE_API=true`
- [ ] Restart backend server
- [ ] Verify health check endpoint shows API integration: `GET /composio-mcp/health`

### **Production Environment**
- [ ] Configure `COMPOSIO_API_KEY` in deployment environment
- [ ] Set appropriate rate limiting (`COMPOSIO_MAX_REQUESTS_PER_MINUTE`)
- [ ] Configure monitoring for API health
- [ ] Set up alerts for API key expiration
- [ ] Document rollback procedures

### **Security Checklist**
- [ ] API key stored in secure environment variables (not in code)
- [ ] API key access limited to necessary services
- [ ] Regular key rotation scheduled
- [ ] Audit logging enabled for API usage
- [ ] Rate limiting configured appropriately

---

## 🧪 **Testing the Integration**

### **1. Health Check**
```bash
curl -X GET http://localhost:8000/composio-mcp/health
```

Expected response:
```json
{
  "status": "healthy",
  "service": "composio_integration",
  "version": "2.0.0",
  "features": {
    "mcp_integration": true,
    "api_integration": true,
    "enhanced_tool_access": true,
    "migration_support": true
  },
  "api_status": {
    "healthy": true,
    "api_key_configured": true
  }
}
```

### **2. Test API Connection Creation**
```bash
curl -X POST http://localhost:8000/composio-mcp/create-api-connection \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"app_key": "gmail", "use_api": true}'
```

### **3. Test Enhanced Tool Discovery**
```bash
curl -X POST http://localhost:8000/composio-mcp/discover-tools-enhanced \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"app_key": "gmail", "use_api": true}'
```

### **4. Run Integration Tests**
```bash
cd backend
python -m pytest tests/test_composio_api_integration.py -v
```

---

## 🔄 **Migration Process**

### **Automated Migration**

The system supports automatic migration from existing MCP connections to enhanced API connections:

```bash
# Check connection status
curl -X GET http://localhost:8000/composio-mcp/connection-status/gmail \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Migrate MCP to API
curl -X POST http://localhost:8000/composio-mcp/migrate-to-api \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"app_key": "gmail", "preserve_tools": true}'
```

### **Manual Migration Steps**

1. **Identify Existing Connections**
   ```bash
   GET /composio-mcp/user-connections
   ```

2. **Check Migration Eligibility**
   ```bash
   GET /composio-mcp/connection-status/{app_key}
   ```

3. **Perform Migration**
   ```bash
   POST /composio-mcp/migrate-to-api
   ```

4. **Verify Enhanced Access**
   ```bash
   POST /composio-mcp/discover-tools-enhanced
   ```

5. **Clean Up Legacy Connections** (Optional)
   ```bash
   POST /composio-mcp/cleanup-legacy
   ```

---

## 📊 **Monitoring and Observability**

### **Key Metrics to Monitor**

1. **API Health**
   - API response times
   - Error rates
   - Rate limiting hits

2. **Tool Usage**
   - API vs MCP tool execution ratio
   - Tool success rates
   - Enhanced feature usage

3. **Migration Progress**
   - Migration success rate
   - User adoption of API features
   - Legacy connection cleanup

### **Logging Configuration**

The integration provides comprehensive logging. Key log events:

```python
# API Client logs
logger.info(f"API connection created for {app_key}")
logger.warning(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
logger.error(f"API error executing tool {tool_name}: {e}")

# Migration logs
logger.info(f"Migrating MCP to API connection for user {user_id}, app {app_key}")
logger.info(f"Successfully migrated {app_key} from MCP to API with {len(enabled_tools)} preserved tools")

# Tool execution logs
logger.info(f"Executing API tool {tool_name} with arguments {arguments}")
logger.info(f"Created {total_tools} dynamic tool methods ({mcp_tools_count} MCP, {api_tools_count} API)")
```

### **Alerting Rules**

Set up alerts for:
- API key expiration warnings
- Rate limit threshold breaches (>80% of limit)
- API error rate >5%
- Migration failures
- Tool execution failures >10%

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **1. API Key Not Working**
```
Error: Authentication failed. Check your API key.
```
**Solution:**
- Verify API key is correctly set in environment
- Check key hasn't expired
- Ensure key has necessary permissions

#### **2. Rate Limiting**
```
Error: Rate limit exceeded. Please wait before retrying.
```
**Solution:**
- Check `COMPOSIO_MAX_REQUESTS_PER_MINUTE` setting
- Implement exponential backoff
- Consider upgrading Composio plan

#### **3. Tool Discovery Fails**
```
Error: Failed to discover tools for app_key
```
**Solution:**
- Verify app_key is supported by Composio
- Check network connectivity
- Validate API key permissions

#### **4. Migration Fails**
```
Error: No existing MCP connection found
```
**Solution:**
- Verify user has existing MCP connection
- Check default agent configuration
- Review connection status endpoint

### **Debug Mode**

Enable detailed logging:

```bash
# Environment variable
export LOG_LEVEL=DEBUG

# Or in code
import logging
logging.getLogger("services.composio_api_client").setLevel(logging.DEBUG)
```

---

## 🔒 **Security Best Practices**

### **API Key Management**
- ✅ Store API keys in secure environment variables
- ✅ Rotate keys regularly (quarterly recommended)
- ✅ Use different keys for different environments
- ✅ Monitor API key usage and access

### **Network Security**
- ✅ Use HTTPS for all API communications
- ✅ Implement proper CORS policies
- ✅ Set up API rate limiting
- ✅ Log all API access attempts

### **Data Protection**
- ✅ Encrypt sensitive connection data
- ✅ Implement proper authentication flows
- ✅ Audit user data access
- ✅ Comply with data retention policies

---

## 📈 **Performance Optimization**

### **Connection Pooling**
The API client uses connection pooling for optimal performance:

```python
# Configuration in ComposioApiClient
self.limits = httpx.Limits(
    max_keepalive_connections=20,
    max_connections=100
)
```

### **Rate Limiting**
Built-in intelligent rate limiting:

```python
# Per-service rate limits
self.limits = {
    "composio": {
        "requests_per_minute": 100,
        "burst_limit": 10,
        "backoff_factor": 2.0
    }
}
```

### **Caching Strategy**
- Tool schemas cached after discovery
- Connected account status cached for 5 minutes
- Rate limit state cached per request window

---

## 🆘 **Support and Resources**

### **Atlas Support**
- Internal documentation: `/docs/composio-integration`
- Team Slack: `#composio-integration`
- Issue tracking: GitHub Issues

### **Composio Resources**
- [Composio Documentation](https://docs.composio.dev)
- [API Reference](https://docs.composio.dev/api-reference)
- [Community Discord](https://discord.gg/composio)
- [Support Email](mailto:<EMAIL>)

### **Quick Links**
- [Composio Dashboard](https://app.composio.dev)
- [API Status Page](https://status.composio.dev)
- [Tool Catalog](https://docs.composio.dev/tools)
- [Integration Examples](https://github.com/ComposioHQ/composio/tree/main/examples)

---

**🎉 You're all set!** Your Composio API integration is now configured for enhanced tool access and improved reliability.