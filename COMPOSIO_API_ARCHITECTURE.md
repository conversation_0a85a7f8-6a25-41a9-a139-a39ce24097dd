# 🏗️ **Composio API Client Architecture Design**

## **Executive Summary**

This document outlines the technical architecture for migrating Atlas's Composio integration from MCP protocol to direct API access. The design maintains 100% backward compatibility while unlocking access to the full Composio tool suite (20+ tools vs current 13).

---

## 🎯 **Design Principles**

### **1. Zero-Disruption Migration**
- ✅ Parallel execution paths (MCP + API)
- ✅ Gradual migration with feature flags
- ✅ Identical user experience
- ✅ Backward compatibility maintained

### **2. Extensible Architecture**
- 🔧 Plugin-based tool execution
- 🔧 Service-agnostic authentication
- 🔧 Configurable integration patterns
- 🔧 Future-proof for additional APIs

### **3. Performance & Reliability**
- ⚡ Connection pooling and caching
- ⚡ Intelligent retry mechanisms
- ⚡ Rate limiting compliance
- ⚡ Comprehensive error handling

---

## 🏛️ **System Architecture**

### **Current State vs Target State**

```mermaid
graph TB
    subgraph "CURRENT (MCP Only)"
        U1[User Request] --> API1[API Layer]
        API1 --> MTW1[MCP Tool Wrapper]
        MTW1 --> MCP1[Composio MCP Server]
        MCP1 --> RES1[13 Tools Available]
    end
    
    subgraph "TARGET (Hybrid MCP + API)"
        U2[User Request] --> API2[API Layer]
        API2 --> ROUTER[Tool Router]
        ROUTER --> MTW2[MCP Tool Wrapper]
        ROUTER --> ATW[API Tool Wrapper]
        MTW2 --> MCP2[Other MCP Servers]
        ATW --> CAPI[Composio Direct API]
        MCP2 --> RES2[Existing Tools]
        CAPI --> RES3[20+ Tools Available]
    end
```

---

## 📁 **File Structure & Components**

### **New Files to Create**

```
backend/
├── services/
│   ├── composio_api_client.py          # 🆕 Core API client
│   ├── api_auth_manager.py             # 🆕 Authentication service
│   └── api_tool_executor.py            # 🆕 Tool execution logic
├── agent/tools/
│   └── api_tool_wrapper.py             # 🆕 API tool wrapper
├── utils/
│   ├── api_error_handler.py            # 🆕 Error handling
│   └── api_rate_limiter.py             # 🆕 Rate limiting
├── config/
│   └── api_configurations.py          # 🆕 API configs
└── tests/
    ├── test_composio_api_client.py     # 🆕 Unit tests
    └── test_api_integration.py         # 🆕 Integration tests
```

### **Files to Modify**

```
backend/
├── services/
│   └── composio_integration.py         # 🔄 Add API methods
├── agent/tools/
│   └── mcp_tool_wrapper.py            # 🔄 Add API routing
├── api/
│   └── composio_mcp.py                # 🔄 Update endpoints
├── agentpress/
│   └── tool_registry.py               # 🔄 Support API tools
└── utils/
    └── environment.py                  # 🔄 Add API configs
```

---

## 🔧 **Core Component Designs**

### **1. ComposioApiClient (Core API Client)**

```python
# /backend/services/composio_api_client.py

from typing import Dict, List, Optional, Any
import httpx
import asyncio
from dataclasses import dataclass
from utils.logger import logger
from utils.api_rate_limiter import RateLimiter
from utils.api_error_handler import APIErrorHandler

@dataclass
class ApiTool:
    """Represents a tool available via Composio API"""
    name: str
    description: str
    parameters: Dict[str, Any]
    app_key: str
    endpoint: str
    method: str
    auth_required: bool = True

@dataclass
class ConnectedAccount:
    """Represents an authenticated user connection to a service"""
    id: str
    app_key: str
    user_id: str
    status: str  # "active", "expired", "revoked"
    auth_data: Dict[str, Any]
    created_at: str
    expires_at: Optional[str] = None

class ComposioApiClient:
    """
    Core client for Composio Direct API integration
    Handles authentication, tool discovery, and execution
    """
    
    def __init__(self):
        self.base_url = "https://backend.composio.dev/api/v1"
        self.api_key = os.getenv("COMPOSIO_API_KEY")
        self.rate_limiter = RateLimiter()
        self.error_handler = APIErrorHandler()
        self._session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self._session = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session:
            await self._session.aclose()
    
    # Authentication Methods
    async def create_connected_account(
        self, 
        app_key: str, 
        user_id: str, 
        redirect_url: str
    ) -> Dict[str, Any]:
        """Create a new connected account with OAuth redirect"""
        
    async def get_oauth_redirect_url(
        self, 
        connected_account_id: str
    ) -> str:
        """Get OAuth redirect URL for user authentication"""
        
    async def verify_connection_status(
        self, 
        connected_account_id: str
    ) -> Dict[str, Any]:
        """Check if connected account is still valid"""
    
    # Tool Discovery Methods
    async def discover_tools(self, app_key: str) -> List[ApiTool]:
        """Discover all available tools for an app via API"""
        
    async def get_tool_schema(
        self, 
        app_key: str, 
        tool_name: str
    ) -> Dict[str, Any]:
        """Get detailed OpenAPI schema for a specific tool"""
    
    # Tool Execution Methods
    async def execute_tool(
        self,
        connected_account_id: str,
        tool_name: str,
        parameters: Dict[str, Any],
        app_key: str
    ) -> Dict[str, Any]:
        """Execute a tool via Composio API"""
        
    # Rate Limiting & Error Handling
    async def _make_authenticated_request(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> httpx.Response:
        """Make authenticated API request with rate limiting"""
```

### **2. ApiToolWrapper (Tool Integration)**

```python
# /backend/agent/tools/api_tool_wrapper.py

from agentpress.tool import Tool, ToolResult
from services.composio_api_client import ComposioApiClient
from services.api_auth_manager import ApiAuthManager
from typing import Dict, Any, List

class ApiToolWrapper(Tool):
    """
    Wrapper for API-based tools (Composio Direct API)
    Integrates with existing AgentPress tool system
    """
    
    def __init__(self, api_configs: List[Dict[str, Any]]):
        self.api_configs = api_configs
        self.api_client = ComposioApiClient()
        self.auth_manager = ApiAuthManager()
        self._api_tools = {}
        super().__init__()
    
    async def _register_api_tools(self):
        """Register API tools dynamically"""
        for config in self.api_configs:
            if config.get('type') == 'composio_api':
                app_key = config['config']['app_key']
                connected_account_id = config['config']['connected_account_id']
                
                # Discover tools via API
                tools = await self.api_client.discover_tools(app_key)
                
                # Register each tool dynamically
                for tool in tools:
                    method_name = f"execute_{tool.name.lower()}"
                    
                    # Create dynamic method
                    def create_tool_method(tool_obj, account_id):
                        async def tool_method(**kwargs):
                            return await self._execute_api_tool(
                                tool_obj.name, kwargs, account_id, tool_obj.app_key
                            )
                        return tool_method
                    
                    # Add method to class
                    setattr(self, method_name, create_tool_method(tool, connected_account_id))
                    
                    # Store tool metadata
                    self._api_tools[tool.name] = {
                        'tool': tool,
                        'connected_account_id': connected_account_id,
                        'method_name': method_name
                    }
    
    async def _execute_api_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        connected_account_id: str,
        app_key: str
    ) -> ToolResult:
        """Execute API tool and return standardized result"""
        try:
            # Verify authentication
            auth_valid = await self.auth_manager.verify_connection(connected_account_id)
            if not auth_valid:
                return ToolResult(
                    success=False,
                    content="Authentication expired. Please reconnect your account.",
                    error="auth_expired"
                )
            
            # Execute via API
            result = await self.api_client.execute_tool(
                connected_account_id=connected_account_id,
                tool_name=tool_name,
                parameters=parameters,
                app_key=app_key
            )
            
            return ToolResult(
                success=True,
                content=result.get('output', 'Tool executed successfully'),
                metadata={
                    'tool_name': tool_name,
                    'execution_id': result.get('execution_id'),
                    'app_key': app_key
                }
            )
            
        except Exception as e:
            logger.error(f"API tool execution failed: {e}")
            return ToolResult(
                success=False,
                content=f"Tool execution failed: {str(e)}",
                error="execution_failed"
            )
```

### **3. Enhanced MCP Tool Wrapper (Routing Logic)**

```python
# Modifications to /backend/agent/tools/mcp_tool_wrapper.py

class MCPToolWrapper(Tool):
    def __init__(self, mcp_configs: Optional[List[Dict[str, Any]]] = None):
        # Existing initialization...
        
        # NEW: Add API tool wrapper
        self.api_configs = [cfg for cfg in mcp_configs if cfg.get('type') == 'composio_api']
        self.api_tool_wrapper = None
        if self.api_configs:
            self.api_tool_wrapper = ApiToolWrapper(self.api_configs)
        
        super().__init__()
    
    async def _ensure_initialized(self):
        # Existing MCP initialization...
        
        # NEW: Initialize API tools
        if self.api_tool_wrapper:
            await self.api_tool_wrapper._register_api_tools()
        
        self._initialized = True
    
    async def _execute_custom_mcp_tool(
        self, 
        tool_name: str, 
        arguments: Dict[str, Any], 
        tool_info: Dict[str, Any]
    ) -> ToolResult:
        """Enhanced to route between MCP and API execution"""
        
        # NEW: Check if this is an API tool
        if tool_info.get('execution_type') == 'composio_api':
            return await self._execute_api_tool(tool_name, arguments, tool_info)
        
        # Existing MCP execution logic...
        custom_type = tool_info.get("customType", "http")
        
        if custom_type == "http":
            return await self._execute_http_mcp_tool(tool_name, arguments, tool_info)
        elif custom_type == "sse":
            return await self._execute_sse_mcp_tool(tool_name, arguments, tool_info)
        # ... rest of existing logic
    
    async def _execute_api_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        tool_info: Dict[str, Any]
    ) -> ToolResult:
        """Route to API tool wrapper"""
        if self.api_tool_wrapper and tool_name in self.api_tool_wrapper._api_tools:
            tool_data = self.api_tool_wrapper._api_tools[tool_name]
            return await self.api_tool_wrapper._execute_api_tool(
                tool_name=tool_name,
                parameters=arguments,
                connected_account_id=tool_data['connected_account_id'],
                app_key=tool_data['tool'].app_key
            )
        else:
            return ToolResult(
                success=False,
                content=f"API tool {tool_name} not found",
                error="tool_not_found"
            )
```

---

## 🔐 **Authentication Architecture**

### **API Authentication Manager**

```python
# /backend/services/api_auth_manager.py

from typing import Dict, Optional
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass

@dataclass
class AuthToken:
    access_token: str
    refresh_token: Optional[str]
    expires_at: datetime
    token_type: str = "Bearer"
    scope: Optional[str] = None

class ApiAuthManager:
    """
    Manages authentication for API-based integrations
    Handles OAuth tokens, refresh cycles, and validation
    """
    
    def __init__(self):
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"), 
            os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        self._token_cache = {}
    
    async def verify_connection(self, connected_account_id: str) -> bool:
        """Verify if connected account is still valid"""
        
    async def get_access_token(self, connected_account_id: str) -> Optional[str]:
        """Get valid access token, refreshing if necessary"""
        
    async def refresh_token(self, connected_account_id: str) -> bool:
        """Refresh expired access token"""
        
    async def store_auth_data(
        self, 
        connected_account_id: str, 
        auth_data: Dict[str, Any]
    ) -> bool:
        """Store authentication data securely"""
        
    async def revoke_connection(self, connected_account_id: str) -> bool:
        """Revoke and clean up connection"""
```

---

## 🛠️ **Tool Discovery & Registration Flow**

### **Enhanced Tool Discovery Process**

```mermaid
sequenceDiagram
    participant USER as User
    participant API as API Layer
    participant CIS as ComposioIntegration
    participant CAC as ComposioApiClient
    participant TR as Tool Registry
    participant ATW as ApiToolWrapper

    USER->>API: Request tool discovery
    API->>CIS: discover_tools_enhanced()
    CIS->>CAC: discover_tools(app_key)
    CAC->>CAC: Fetch from Composio API
    CAC-->>CIS: 20+ tools returned
    CIS->>TR: Register API tools
    TR->>ATW: Create dynamic methods
    ATW-->>API: Tool schemas generated
    API-->>USER: Enhanced tool list
```

### **Tool Registration Enhancement**

```python
# Modifications to /backend/agentpress/tool_registry.py

class ToolRegistry:
    def register_tool(
        self, 
        tool_class, 
        function_names=None, 
        tool_type="native",  # NEW: Support tool types
        **kwargs
    ):
        """Enhanced to support API tools alongside MCP tools"""
        
        # Existing registration logic...
        
        # NEW: Handle API tool registration
        if tool_type == "api":
            self._register_api_tool(tool_class, function_names, **kwargs)
        elif tool_type == "mcp":
            self._register_mcp_tool(tool_class, function_names, **kwargs)
        else:
            self._register_native_tool(tool_class, function_names, **kwargs)
    
    def _register_api_tool(self, tool_class, function_names, **kwargs):
        """Register API-based tools with enhanced metadata"""
        # API-specific registration logic
        pass
```

---

## 📊 **Data Flow & Storage**

### **Enhanced Storage Schema**

```python
# Updated storage format for agents.custom_mcps

# Current MCP Format (Preserved)
{
  "type": "http",
  "name": "Gmail",
  "config": {
    "url": "https://mcp.composio.dev/partner/composio/gmail/mcp?customerId=uuid"
  },
  "enabledTools": ["GMAIL_SEND_EMAIL"]
}

# New API Format (Added)
{
  "type": "composio_api",
  "name": "Gmail Enhanced",
  "config": {
    "app_key": "gmail",
    "connected_account_id": "conn_abc123",
    "user_id": "user_456",
    "auth_status": "active"
  },
  "enabledTools": [
    "GMAIL_SEND_EMAIL",
    "GMAIL_CREATE_DRAFT", 
    "GMAIL_SCHEDULE_EMAIL",
    "GMAIL_BULK_OPERATIONS",
    # ... 20+ tools available
  ],
  "metadata": {
    "execution_type": "api",
    "tool_count": 23,
    "last_auth_check": "2025-06-24T19:00:00Z"
  }
}
```

---

## ⚡ **Performance & Error Handling**

### **Rate Limiting Strategy**

```python
# /backend/utils/api_rate_limiter.py

class RateLimiter:
    """
    Intelligent rate limiting for API requests
    Respects service-specific limits and implements backoff
    """
    
    def __init__(self):
        self.limits = {
            "composio": {
                "requests_per_minute": 100,
                "burst_limit": 10,
                "backoff_factor": 2.0
            }
        }
        self._request_history = {}
    
    async def check_rate_limit(self, service: str, user_id: str) -> bool:
        """Check if request is within rate limits"""
        
    async def apply_backoff(self, service: str, attempt: int) -> None:
        """Apply exponential backoff for failed requests"""
```

### **Error Handling Strategy**

```python
# /backend/utils/api_error_handler.py

class APIErrorHandler:
    """
    Comprehensive error handling for API integrations
    Provides user-friendly messages and retry logic
    """
    
    ERROR_MAPPINGS = {
        401: "Authentication failed. Please reconnect your account.",
        403: "Insufficient permissions. Check your app permissions.",
        429: "Rate limit exceeded. Please wait before retrying.",
        500: "Service temporarily unavailable. Please try again later.",
        503: "Service under maintenance. Please check back soon."
    }
    
    async def handle_api_error(
        self, 
        response: httpx.Response, 
        context: Dict[str, Any]
    ) -> ToolResult:
        """Convert API errors to user-friendly tool results"""
        
    async def should_retry(self, error_code: int, attempt: int) -> bool:
        """Determine if request should be retried"""
```

---

## 🧪 **Testing Strategy**

### **Unit Test Structure**

```python
# /backend/tests/test_composio_api_client.py

class TestComposioApiClient:
    async def test_create_connected_account(self):
        """Test account creation flow"""
        
    async def test_tool_discovery(self):
        """Test tool discovery returns enhanced tool set"""
        
    async def test_tool_execution(self):
        """Test successful tool execution"""
        
    async def test_authentication_handling(self):
        """Test OAuth flow and token management"""
        
    async def test_error_scenarios(self):
        """Test various error conditions and recovery"""
        
    async def test_rate_limiting(self):
        """Test rate limiting compliance"""
```

### **Integration Test Structure**

```python
# /backend/tests/test_api_integration.py

class TestApiIntegration:
    async def test_end_to_end_flow(self):
        """Test complete user flow: discovery → auth → execution"""
        
    async def test_backward_compatibility(self):
        """Ensure MCP tools continue working"""
        
    async def test_migration_scenarios(self):
        """Test migration from MCP to API"""
        
    async def test_performance_comparison(self):
        """Compare API vs MCP performance"""
```

---

## 🚀 **Migration Strategy**

### **Phase 1: Infrastructure (Week 1)**
- ✅ Deploy API client infrastructure
- ✅ Add authentication services
- ✅ Implement rate limiting and error handling
- ✅ Create test suite

### **Phase 2: Backend Integration (Week 2)**
- ✅ Integrate API tool wrapper
- ✅ Update tool registry
- ✅ Enhance discovery endpoints
- ✅ Add storage migration

### **Phase 3: User Migration (Week 3)**
- ✅ Feature flag rollout
- ✅ User interface updates
- ✅ Documentation updates
- ✅ Support for both MCP and API

### **Phase 4: Optimization (Week 4)**
- ✅ Performance optimization
- ✅ Advanced features
- ✅ Analytics and monitoring
- ✅ Full API catalog support

---

## 📈 **Success Metrics**

### **Functional Metrics**
- **Tool Availability**: 13 → 20+ tools per Composio app
- **Success Rate**: >99% for tool executions
- **Authentication**: <5% auth failure rate

### **Performance Metrics**
- **Latency**: <2s average execution time
- **Throughput**: 1000+ concurrent executions
- **Reliability**: 99.9% uptime

### **User Experience Metrics**
- **Zero Breaking Changes**: 100% backward compatibility
- **Enhanced Functionality**: Access to premium tool features
- **Seamless Migration**: <1% user reports issues

---

This architecture design ensures a robust, scalable, and user-friendly migration from MCP to direct API access while maintaining the high-quality experience Atlas users expect.