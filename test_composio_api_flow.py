#!/usr/bin/env python3
"""
Comprehensive Test Script for Composio API Authentication Flow

This script tests the complete end-to-end flow:
1. API connection creation
2. OAuth authentication URL generation
3. Tool discovery (20+ tools vs 13 MCP)
4. Tool selection and storage
5. Authentication verification

Usage:
    python test_composio_api_flow.py --app gmail --user test_user_123
"""

import asyncio
import json
import httpx
import os
import sys
from typing import Dict, Any
import argparse
from datetime import datetime

# Base URL for Atlas backend
ATLAS_BASE_URL = os.getenv("ATLAS_BASE_URL", "http://localhost:8000")
COMPOSIO_API_KEY = os.getenv("COMPOSIO_API_KEY")

# Colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_step(step: str, status: str = "INFO"):
    color = Colors.OKBLUE if status == "INFO" else Colors.OKGREEN if status == "SUCCESS" else Colors.FAIL
    print(f"{color}[{status}] {step}{Colors.ENDC}")

def print_json(data: Dict[str, Any], title: str = "Response"):
    print(f"{Colors.OKCYAN}{title}:{Colors.ENDC}")
    print(json.dumps(data, indent=2))
    print()

async def test_health_check():
    """Test API health and feature availability"""
    print_step("Testing API health check")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{ATLAS_BASE_URL}/composio-mcp/health")
            data = response.json()
            
            if response.status_code == 200:
                print_step("Health check passed", "SUCCESS")
                print_json(data, "Health Status")
                
                # Check required features
                features = data.get("features", {})
                if features.get("api_integration"):
                    print_step("✅ API integration enabled", "SUCCESS")
                else:
                    print_step("❌ API integration disabled", "FAIL")
                    return False
                    
                if features.get("migration_support"):
                    print_step("✅ Migration support available", "SUCCESS")
                else:
                    print_step("⚠️ Migration support not available", "WARNING")
                
                return True
            else:
                print_step(f"Health check failed: {response.status_code}", "FAIL")
                return False
                
        except Exception as e:
            print_step(f"Health check error: {e}", "FAIL")
            return False

async def test_create_api_connection(app_key: str, user_id: str, jwt_token: str):
    """Test API connection creation"""
    print_step(f"Creating API connection for {app_key}")
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    payload = {"app_key": app_key}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{ATLAS_BASE_URL}/composio-mcp/create-connection",
                json=payload,
                headers=headers
            )
            
            data = response.json()
            print_json(data, "Connection Creation Response")
            
            if response.status_code == 200 and data.get("success"):
                print_step("✅ API connection created successfully", "SUCCESS")
                
                # Extract important info
                auth_url = data.get("auth_url")
                connected_account_id = data.get("session_uuid")
                
                if auth_url:
                    print_step(f"🔗 OAuth URL: {auth_url}", "INFO")
                    print_step("👆 Open this URL in browser to complete authentication", "INFO")
                
                return {
                    "success": True,
                    "connected_account_id": connected_account_id,
                    "auth_url": auth_url
                }
            else:
                print_step(f"❌ Connection creation failed: {data.get('error')}", "FAIL")
                return {"success": False, "error": data.get("error")}
                
        except Exception as e:
            print_step(f"Connection creation error: {e}", "FAIL")
            return {"success": False, "error": str(e)}

async def test_tool_discovery(app_key: str, jwt_token: str):
    """Test enhanced tool discovery via API"""
    print_step(f"Discovering tools for {app_key} via API")
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    payload = {"app_key": app_key}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{ATLAS_BASE_URL}/composio-mcp/discover-tools",
                json=payload,
                headers=headers
            )
            
            data = response.json()
            
            if response.status_code == 200 and data.get("success"):
                tool_count = data.get("count", 0)
                tools = data.get("tools", [])
                
                print_step(f"✅ Discovered {tool_count} tools via API", "SUCCESS")
                
                # Show first few tools as examples
                if tools:
                    print_step("Sample tools discovered:", "INFO")
                    for i, tool in enumerate(tools[:5]):
                        print(f"  {i+1}. {tool.get('name', 'Unknown')} - {tool.get('description', 'No description')}")
                    
                    if len(tools) > 5:
                        print(f"  ... and {len(tools) - 5} more tools")
                
                return {
                    "success": True,
                    "count": tool_count,
                    "tools": tools
                }
            else:
                print_step(f"❌ Tool discovery failed: {data.get('error')}", "FAIL")
                return {"success": False, "error": data.get("error")}
                
        except Exception as e:
            print_step(f"Tool discovery error: {e}", "FAIL")
            return {"success": False, "error": str(e)}

async def test_tool_selection(app_key: str, tools: list, jwt_token: str):
    """Test tool selection and storage"""
    print_step(f"Selecting and storing tools for {app_key}")
    
    # Select first 5 tools for testing
    selected_tools = [tool.get("name") for tool in tools[:5] if tool.get("name")]
    
    if not selected_tools:
        print_step("❌ No tools available for selection", "FAIL")
        return {"success": False, "error": "No tools available"}
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "app_key": app_key,
        "selected_tools": selected_tools
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{ATLAS_BASE_URL}/composio-mcp/update-tools",
                json=payload,
                headers=headers
            )
            
            data = response.json()
            print_json(data, "Tool Selection Response")
            
            if response.status_code == 200 and data.get("success"):
                enabled_count = len(data.get("enabled_tools", []))
                print_step(f"✅ Enabled {enabled_count} tools successfully", "SUCCESS")
                
                print_step("Enabled tools:", "INFO")
                for tool in selected_tools:
                    print(f"  • {tool}")
                
                return {"success": True, "enabled_tools": selected_tools}
            else:
                print_step(f"❌ Tool selection failed: {data.get('error')}", "FAIL")
                return {"success": False, "error": data.get("error")}
                
        except Exception as e:
            print_step(f"Tool selection error: {e}", "FAIL")
            return {"success": False, "error": str(e)}

async def test_connection_status(app_key: str, jwt_token: str):
    """Test connection status check"""
    print_step(f"Checking connection status for {app_key}")
    
    headers = {"Authorization": f"Bearer {jwt_token}"}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{ATLAS_BASE_URL}/composio-mcp/connection-status/{app_key}",
                headers=headers
            )
            
            data = response.json()
            print_json(data, "Connection Status")
            
            if response.status_code == 200 and data.get("success"):
                has_api = data.get("has_api", False)
                has_mcp = data.get("has_mcp", False)
                recommendation = data.get("recommendation")
                
                print_step(f"✅ Status check completed", "SUCCESS")
                print_step(f"API Connection: {'✅' if has_api else '❌'}", "INFO")
                print_step(f"MCP Connection: {'✅' if has_mcp else '❌'}", "INFO")
                
                if recommendation:
                    print_step(f"Recommendation: {recommendation}", "INFO")
                
                return {"success": True, "status": data}
            else:
                print_step(f"❌ Status check failed: {data.get('error')}", "FAIL")
                return {"success": False, "error": data.get("error")}
                
        except Exception as e:
            print_step(f"Status check error: {e}", "FAIL")
            return {"success": False, "error": str(e)}

async def test_authentication_initiation(app_key: str, jwt_token: str):
    """Test authentication initiation"""
    print_step(f"Initiating authentication for {app_key}")
    
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    payload = {"app_key": app_key}
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{ATLAS_BASE_URL}/composio-mcp/initiate-auth",
                json=payload,
                headers=headers
            )
            
            data = response.json()
            print_json(data, "Authentication Response")
            
            if response.status_code == 200 and data.get("success"):
                redirect_url = data.get("redirect_url")
                connection_id = data.get("connection_id")
                
                print_step("✅ Authentication initiated", "SUCCESS")
                
                if redirect_url:
                    print_step(f"🔗 OAuth URL: {redirect_url}", "INFO")
                    print_step("👆 User should open this URL to complete authentication", "INFO")
                
                if connection_id:
                    print_step(f"🔑 Connected Account ID: {connection_id}", "INFO")
                
                return {
                    "success": True,
                    "auth_url": redirect_url,
                    "connection_id": connection_id
                }
            else:
                print_step(f"❌ Authentication initiation failed: {data.get('error')}", "FAIL")
                return {"success": False, "error": data.get("error")}
                
        except Exception as e:
            print_step(f"Authentication error: {e}", "FAIL")
            return {"success": False, "error": str(e)}

def generate_test_jwt():
    """Generate a test JWT token (for testing purposes only)"""
    # In a real scenario, you'd get this from your authentication system
    import base64
    
    # Simple test token (NOT FOR PRODUCTION)
    payload = {
        "user_id": "test_user_123",
        "exp": **********  # Far future expiry for testing
    }
    
    # Base64 encode the payload (this is NOT a secure JWT)
    token_payload = base64.b64encode(json.dumps(payload).encode()).decode()
    return f"test.{token_payload}.signature"

async def main():
    parser = argparse.ArgumentParser(description="Test Composio API authentication flow")
    parser.add_argument("--app", default="gmail", help="App to test (default: gmail)")
    parser.add_argument("--user", default="test_user_123", help="User ID for testing")
    parser.add_argument("--jwt", help="JWT token (will generate test token if not provided)")
    parser.add_argument("--skip-auth", action="store_true", help="Skip authentication steps")
    
    args = parser.parse_args()
    
    app_key = args.app
    user_id = args.user
    jwt_token = args.jwt or generate_test_jwt()
    
    print(f"{Colors.HEADER}{'='*60}")
    print(f"🧪 COMPOSIO API AUTHENTICATION FLOW TEST")
    print(f"{'='*60}{Colors.ENDC}")
    print(f"App: {app_key}")
    print(f"User: {user_id}")
    print(f"Time: {datetime.now().isoformat()}")
    print()
    
    # Check prerequisites
    if not COMPOSIO_API_KEY:
        print_step("❌ COMPOSIO_API_KEY not set in environment", "FAIL")
        print_step("Set it with: export COMPOSIO_API_KEY=your_key_here", "INFO")
        sys.exit(1)
    
    # Test sequence
    results = {}
    
    # 1. Health check
    health_ok = await test_health_check()
    if not health_ok:
        print_step("❌ Health check failed, stopping tests", "FAIL")
        sys.exit(1)
    
    # 2. Create API connection
    print_step("\n" + "="*50)
    connection_result = await test_create_api_connection(app_key, user_id, jwt_token)
    results["connection"] = connection_result
    
    if not connection_result.get("success"):
        print_step("❌ Connection creation failed, stopping tests", "FAIL")
        sys.exit(1)
    
    # 3. Tool discovery
    print_step("\n" + "="*50)
    discovery_result = await test_tool_discovery(app_key, jwt_token)
    results["discovery"] = discovery_result
    
    if discovery_result.get("success"):
        tools = discovery_result.get("tools", [])
        
        # 4. Tool selection
        print_step("\n" + "="*50)
        selection_result = await test_tool_selection(app_key, tools, jwt_token)
        results["selection"] = selection_result
    
    # 5. Connection status
    print_step("\n" + "="*50)
    status_result = await test_connection_status(app_key, jwt_token)
    results["status"] = status_result
    
    # 6. Authentication initiation (if not skipped)
    if not args.skip_auth:
        print_step("\n" + "="*50)
        auth_result = await test_authentication_initiation(app_key, jwt_token)
        results["authentication"] = auth_result
    
    # Summary
    print_step("\n" + "="*60, "INFO")
    print_step("🏁 TEST SUMMARY", "INFO")
    print_step("="*60, "INFO")
    
    success_count = sum(1 for result in results.values() if result.get("success"))
    total_tests = len(results)
    
    print_step(f"✅ {success_count}/{total_tests} tests passed", "SUCCESS" if success_count == total_tests else "WARNING")
    
    for test_name, result in results.items():
        status = "✅" if result.get("success") else "❌"
        print_step(f"{status} {test_name.title()}", "INFO")
    
    if success_count == total_tests:
        print_step("\n🎉 All tests passed! Composio API integration is working correctly.", "SUCCESS")
    else:
        print_step(f"\n⚠️ {total_tests - success_count} tests failed. Check the errors above.", "WARNING")
    
    # Next steps
    print_step("\n📋 NEXT STEPS:", "INFO")
    print_step("1. Complete OAuth authentication using the provided URLs", "INFO")
    print_step("2. Test tool execution with authenticated connections", "INFO")
    print_step("3. Verify tool mentions work in agent conversations", "INFO")
    print_step("4. Check frontend integration displays enhanced tool access", "INFO")

if __name__ == "__main__":
    asyncio.run(main())