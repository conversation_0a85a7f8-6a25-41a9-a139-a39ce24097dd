"""
Composio Integration API Endpoints

Enhanced API endpoints that support both MCP and Direct API integration with Composio.

Features:
- Legacy MCP URL generation and management
- NEW: Direct API integration for enhanced tool access (20+ tools vs 13 MCP tools)
- Unified authentication flow
- Backward compatibility maintained
- Gradual migration support
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import httpx
from utils.auth_utils import get_current_user_id_from_jwt
from utils.logger import logger
from services.composio_integration import (
    composio_mcp_service,
    ComposioMCPConnection,
    composio_tool_executor,
)
from services.mcp_custom import discover_custom_tools

router = APIRouter(prefix="/composio-mcp", tags=["composio-mcp"])


class CreateMCPConnectionRequest(BaseModel):
    """Request model for creating a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class CreateMCPConnectionResponse(BaseModel):
    """Response model for MCP connection creation"""

    success: bool
    app_key: str
    qualified_name: Optional[str] = None
    mcp_url: Optional[str] = None
    auth_url: Optional[str] = None
    session_uuid: Optional[str] = None
    error: Optional[str] = None
    message: str


class ListUserConnectionsResponse(BaseModel):
    """Response model for listing user's Composio MCP connections"""

    success: bool
    connections: List[Dict[str, Any]]
    total: int


class DiscoverComposioToolsRequest(BaseModel):
    """Request model for discovering tools from a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class DiscoverComposioToolsResponse(BaseModel):
    """Response model for Composio MCP tool discovery"""

    success: bool
    app_key: str
    tools: List[Dict[str, Any]]
    count: int
    mcp_url: Optional[str] = None
    error: Optional[str] = None


class UpdateComposioToolsRequest(BaseModel):
    """Request model for updating selected tools for a Composio MCP connection"""

    app_key: str
    selected_tools: List[str]  # List of tool names to enable


class UpdateComposioToolsResponse(BaseModel):
    """Response model for updating Composio MCP tools"""

    success: bool
    app_key: str
    enabled_tools: List[str]
    message: str
    error: Optional[str] = None


class GetSupportedAppsResponse(BaseModel):
    """Response model for getting supported Composio apps"""

    success: bool
    apps: List[Dict[str, Any]]
    total: int
    message: str


class InitiateAuthRequest(BaseModel):
    """Request model for initiating authentication for a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class InitiateAuthResponse(BaseModel):
    """Response model for Composio MCP authentication initiation"""

    success: bool
    app_key: str
    tool_name: str
    redirect_url: Optional[str] = None
    connection_id: Optional[str] = None
    instruction: Optional[str] = None
    message: str
    error: Optional[str] = None


# NEW: Enhanced API Integration Models

class CreateApiConnectionRequest(BaseModel):
    """Request model for creating a Composio API connection"""
    
    app_key: str  # e.g., "gmail", "slack", "github"
    use_api: bool = True  # Force API usage
    redirect_url: Optional[str] = None


class CreateApiConnectionResponse(BaseModel):
    """Response model for API connection creation"""
    
    success: bool
    app_key: str
    connection_type: str  # "api" or "mcp"
    connected_account_id: Optional[str] = None
    auth_url: Optional[str] = None
    error: Optional[str] = None
    message: str
    enhanced_features: bool = False  # True if API provides more tools


class DiscoverToolsEnhancedRequest(BaseModel):
    """Enhanced request model for tool discovery"""
    
    app_key: str
    use_api: Optional[bool] = None  # None = auto-detect, True = force API, False = force MCP
    connected_account_id: Optional[str] = None


class DiscoverToolsEnhancedResponse(BaseModel):
    """Enhanced response model for tool discovery"""
    
    success: bool
    app_key: str
    tools: List[Dict[str, Any]]
    count: int
    method: str  # "api", "mcp", or "hybrid"
    enhanced_access: bool = False  # True if using API
    comparison: Optional[Dict[str, Any]] = None  # API vs MCP comparison
    error: Optional[str] = None


class StoreApiConnectionRequest(BaseModel):
    """Request model for storing API connection"""
    
    app_key: str
    connected_account_id: str
    selected_tools: List[str] = []


class StoreApiConnectionResponse(BaseModel):
    """Response model for storing API connection"""
    
    success: bool
    app_key: str
    tools_enabled: int
    message: str
    error: Optional[str] = None


@router.post("/create-connection", response_model=CreateMCPConnectionResponse)
async def create_composio_connection(
    request: CreateMCPConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Create a Composio API connection (Enhanced - 20+ tools vs 13 MCP tools)

    This endpoint:
    1. Creates a connected account via Composio Direct API
    2. Provides OAuth authentication URL for user
    3. Enables access to full tool catalog (20+ tools per app)
    4. Handles authentication state management

    Args:
        request: Contains the app_key (e.g., "gmail", "slack")
        user_id: Current user ID from JWT token

    Returns:
        Response with auth_url for OAuth authentication
    """
    try:
        logger.info(
            f"Creating Composio API connection for user {user_id}, app {request.app_key}"
        )

        # Create API connection with enhanced tool access
        connection = await composio_mcp_service.create_api_connection(
            user_id=user_id, 
            app_key=request.app_key,
            redirect_url="https://your-app.com/auth/callback"  # TODO: Make configurable
        )

        if connection.success:
            logger.info(f"Successfully created API connection for {request.app_key}")
            return CreateMCPConnectionResponse(
                success=True,
                app_key=connection.app_key,
                qualified_name=connection.qualified_name,
                mcp_url=None,  # Not applicable for API connections
                auth_url=connection.auth_url,
                session_uuid=connection.session_uuid,  # This is the connected account ID
                message=f"Successfully created API connection for {request.app_key}. Enhanced tool access enabled (20+ tools). Use auth_url to authenticate.",
            )
        else:
            logger.error(f"Failed to create API connection: {connection.error}")
            return CreateMCPConnectionResponse(
                success=False,
                app_key=connection.app_key,
                error=connection.error,
                message=f"Failed to create API connection for {request.app_key}",
            )

    except Exception as e:
        logger.error(f"Error in create_composio_connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user-connections", response_model=ListUserConnectionsResponse)
async def list_user_composio_connections(
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    List all Composio MCP connections for the current user.

    Returns connections stored in the mcp_oauth_tokens table with
    qualified_name starting with "composio/".
    """
    try:
        logger.info(f"Listing Composio MCP connections for user {user_id}")

        # Use the service method to get connections
        connections = await composio_mcp_service.list_user_mcp_connections(user_id)

        return ListUserConnectionsResponse(
            success=True, connections=connections, total=len(connections)
        )

    except Exception as e:
        logger.error(f"Error listing user connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/connection/{app_key}")
async def delete_composio_connection(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Delete a Composio MCP connection for the user.

    Args:
        app_key: The app key to delete (e.g., "gmail")
        user_id: Current user ID from JWT token
    """
    try:
        logger.info(
            f"Deleting Composio MCP connection for user {user_id}, app {app_key}"
        )

        # Use the service method to delete connection
        deleted = await composio_mcp_service.delete_user_mcp_connection(
            user_id, app_key
        )

        if deleted:
            return {
                "success": True,
                "message": f"Successfully deleted connection for {app_key}",
            }
        else:
            return {"success": False, "message": f"No connection found for {app_key}"}

    except Exception as e:
        logger.error(f"Error deleting connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/supported-apps", response_model=GetSupportedAppsResponse)
async def get_supported_apps():
    """
    Get list of supported Composio apps from the official Composio API.

    This endpoint fetches all available MCP servers from mcp.composio.dev
    and returns them in our standardized format.
    """
    try:
        # Fetch apps from official Composio API
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get("https://mcp.composio.dev/api/apps")
            response.raise_for_status()
            composio_apps = response.json()

        # Transform Composio apps to our format
        supported_apps = []
        for app in composio_apps:
            # Map Composio categories to our simplified categories
            category_mapping = {
                "popular": "popular",
                "collaboration & communication": "communication",
                "developer tools & devops": "development",
                "productivity & project management": "productivity",
                "ai & machine learning": "ai",
                "analytics & data": "analytics",
                "marketing & social media": "marketing",
                "crm": "crm",
                "finance & accounting": "finance",
                "document & file management": "storage",
                "scheduling & booking": "scheduling",
                "entertainment & media": "media",
                "education & lms": "education",
                "design & creative tools": "design",
                "social": "social",
                "gaming": "gaming",
                "voice": "voice",
                "email": "email",
                "other / miscellaneous": "other",
                "workflow automation": "automation",
                "sales & customer support": "support",
                "security & compliance": "security",
                "monitoring & observability": "monitoring",
                "time tracking": "productivity",
                "url shortening": "utilities",
                "email marketing": "marketing",
                "website builders": "development",
                "browser automation": "automation",
                "testing": "development",
                "data services": "analytics",
                "financial data": "finance",
                "internet security": "security",
                "database management": "development",
                "vacation rental software": "business",
                "lead generation": "marketing",
                "sales": "sales",
                "incident management": "monitoring",
                "natural language processing": "ai",
                "web scraping": "analytics",
                "web3 development": "development",
                "compliance": "security",
                "networking": "development",
                "cdn": "development",
                "dns": "development",
            }

            # Get the primary category from meta.categories or fallback to app.category
            primary_category = "other"
            if app.get("meta", {}).get("categories"):
                primary_category = app["meta"]["categories"][0].get("name", "other")
            elif app.get("category"):
                primary_category = app["category"]

            # Map to our simplified category
            mapped_category = category_mapping.get(primary_category, "other")

            # Use icon from meta.logo or fallback to a default emoji
            icon_url = app.get("meta", {}).get("logo") or app.get("icon", "")

            supported_apps.append(
                {
                    "key": app["key"],
                    "name": app["name"],
                    "description": app.get("description", f"Connect to {app['name']}"),
                    "icon": icon_url,  # Use the actual icon URL from Composio
                    "category": mapped_category,
                    "tool_count": app.get("meta", {}).get("tool_count", 0),
                    "usage_count": app.get("usageCount", 0),
                    "popular": app.get("popular", False),
                }
            )

        logger.info(f"Fetched {len(supported_apps)} apps from Composio API")

        return GetSupportedAppsResponse(
            success=True,
            apps=supported_apps,
            total=len(supported_apps),
            message=f"Successfully fetched {len(supported_apps)} supported apps from Composio",
        )

    except Exception as e:
        logger.error(f"Error fetching apps from Composio API: {e}")
        # Fallback to a minimal set if API fails
        fallback_apps = [
            {
                "key": "gmail",
                "name": "Gmail",
                "description": "Connect to Gmail for email management",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg",
                "category": "communication",
                "tool_count": 23,
                "usage_count": 196,
                "popular": True,
            },
            {
                "key": "github",
                "name": "GitHub",
                "description": "Connect to GitHub for code management",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png",
                "category": "development",
                "tool_count": 910,
                "usage_count": 470,
                "popular": True,
            },
            {
                "key": "slack",
                "name": "Slack",
                "description": "Connect to Slack for team communication",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg",
                "category": "communication",
                "tool_count": 174,
                "usage_count": 44,
                "popular": True,
            },
        ]

        return GetSupportedAppsResponse(
            success=True,
            apps=fallback_apps,
            total=len(fallback_apps),
            message=f"Using fallback apps due to API error: {str(e)}",
        )


@router.post("/discover-tools", response_model=DiscoverComposioToolsResponse)
async def discover_composio_tools(
    request: DiscoverComposioToolsRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> DiscoverComposioToolsResponse:
    """
    Discover available tools via Composio Direct API (Enhanced - 20+ tools)

    This endpoint uses the Direct API for tool discovery, providing access to
    the complete tool catalog rather than the limited MCP subset.
    """
    try:
        logger.info(
            f"Discovering tools for Composio app: {request.app_key}, user: {user_id}"
        )

        # Use enhanced tool discovery via API
        discovery_result = await composio_mcp_service.discover_tools_enhanced(
            app_key=request.app_key,
            use_api=True  # Force API usage for enhanced tool access
        )

        if discovery_result["success"]:
            logger.info(
                f"Discovered {discovery_result['count']} tools for {request.app_key} via API"
            )

            return DiscoverComposioToolsResponse(
                success=True,
                app_key=request.app_key,
                tools=discovery_result["tools"],
                count=discovery_result["count"],
                mcp_url=None,  # Not applicable for API discovery
            )
        else:
            return DiscoverComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                tools=[],
                count=0,
                error=discovery_result.get("error", "Tool discovery failed"),
            )

    except Exception as e:
        logger.error(f"Error discovering Composio tools for {request.app_key}: {e}")
        return DiscoverComposioToolsResponse(
            success=False, app_key=request.app_key, tools=[], count=0, error=str(e)
        )


@router.post("/update-tools", response_model=UpdateComposioToolsResponse)
async def update_composio_tools(
    request: UpdateComposioToolsRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> UpdateComposioToolsResponse:
    """
    Update the selected tools for a Composio API connection.

    This stores the API connection with selected tools in the default agent,
    enabling access to the enhanced tool catalog (20+ tools per app).
    """
    try:
        logger.info(
            f"Updating tools for Composio app: {request.app_key}, user: {user_id}"
        )
        logger.info(f"Selected tools: {request.selected_tools}")

        # First, get the connected account ID for this user/app
        connection_status = await composio_mcp_service.get_connection_status(
            user_id=user_id, 
            app_key=request.app_key
        )
        
        if not connection_status.get("has_api"):
            return UpdateComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                enabled_tools=[],
                message="No API connection found. Please create connection first.",
                error="API connection required for enhanced tool access",
            )
        
        # Get connected account ID from existing API connection
        api_connection = connection_status.get("connections", {}).get("api", {})
        connected_account_id = api_connection.get("connected_account_id")
        
        if not connected_account_id:
            return UpdateComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                enabled_tools=[],
                message="Invalid API connection state",
                error="Connected account ID not found",
            )

        # Store API connection with selected tools
        success = await composio_mcp_service.store_api_connection(
            user_id=user_id,
            app_key=request.app_key,
            connected_account_id=connected_account_id,
            selected_tools=request.selected_tools
        )

        if success:
            return UpdateComposioToolsResponse(
                success=True,
                app_key=request.app_key,
                enabled_tools=request.selected_tools,
                message=f"Successfully enabled {len(request.selected_tools)} tools for {request.app_key} (Enhanced API access)",
            )
        else:
            return UpdateComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                enabled_tools=[],
                message="Failed to update enabled tools",
                error="Could not store API connection configuration",
            )

    except Exception as e:
        logger.error(f"Error updating Composio tools for {request.app_key}: {e}")
        return UpdateComposioToolsResponse(
            success=False,
            app_key=request.app_key,
            enabled_tools=[],
            message="Failed to update enabled tools",
            error=str(e),
        )


@router.post("/initiate-auth", response_model=InitiateAuthResponse)
async def initiate_composio_auth(
    request: InitiateAuthRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> InitiateAuthResponse:
    """
    Initiate authentication for a Composio API connection.

    This endpoint uses the OAuth flow from the API connection creation,
    providing the redirect URL for user authentication.

    Args:
        request: Contains the app_key (e.g., "gmail", "slack")
        user_id: Current user ID from JWT token

    Returns:
        Response with redirect_url for OAuth authentication
    """
    try:
        logger.info(
            f"Initiating authentication for Composio app: {request.app_key}, user: {user_id}"
        )

        # Check if API connection already exists
        connection_status = await composio_mcp_service.get_connection_status(
            user_id=user_id, 
            app_key=request.app_key
        )
        
        if connection_status.get("has_api"):
            # Connection exists, check if authenticated
            api_connection = connection_status.get("connections", {}).get("api", {})
            connected_account_id = api_connection.get("connected_account_id")
            
            if connected_account_id:
                # Verify connection status with Composio
                if composio_mcp_service.api_client:
                    async with composio_mcp_service.api_client as client:
                        verification = await client.verify_connection_status(connected_account_id)
                        
                        if verification.get("valid"):
                            return InitiateAuthResponse(
                                success=True,
                                app_key=request.app_key,
                                tool_name="CONNECTION_STATUS",
                                connection_id=connected_account_id,
                                message=f"Connection already authenticated for {request.app_key}",
                            )

        # Create new API connection for authentication
        connection = await composio_mcp_service.create_api_connection(
            user_id=user_id,
            app_key=request.app_key,
            redirect_url="https://your-app.com/auth/callback"
        )

        if connection.success and connection.auth_url:
            logger.info(
                f"Successfully created auth URL for {request.app_key}: {connection.auth_url}"
            )
            return InitiateAuthResponse(
                success=True,
                app_key=request.app_key,
                tool_name="API_CONNECTION",
                redirect_url=connection.auth_url,
                connection_id=connection.session_uuid,
                instruction=f"Complete OAuth authentication for {request.app_key}",
                message=f"Authentication URL created for {request.app_key}. Use redirect_url to complete OAuth.",
            )
        else:
            logger.error(
                f"Failed to create auth URL for {request.app_key}: {connection.error}"
            )
            return InitiateAuthResponse(
                success=False,
                app_key=request.app_key,
                tool_name="API_CONNECTION",
                message=f"Failed to create authentication URL for {request.app_key}",
                error=connection.error,
            )

    except Exception as e:
        logger.error(f"Error in initiate_composio_auth: {e}")
        return InitiateAuthResponse(
            success=False,
            app_key=request.app_key,
            tool_name="API_CONNECTION",
            message=f"Failed to initiate authentication for {request.app_key}",
            error=str(e),
        )


@router.post("/refresh-connection/{app_key}")
async def refresh_mcp_connection(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Refresh MCP connection after OAuth authentication is completed.

    This endpoint should be called after the user completes OAuth authentication
    to ensure the MCP server URL reflects the authenticated state.
    """
    try:
        logger.info(f"Refreshing MCP connection for user {user_id}, app {app_key}")

        success = await composio_mcp_service.refresh_mcp_connection_after_auth(
            user_id, app_key
        )

        if success:
            return {
                "success": True,
                "message": f"Successfully refreshed MCP connection for {app_key}",
                "app_key": app_key,
            }
        else:
            return {
                "success": False,
                "message": f"Failed to refresh MCP connection for {app_key}",
                "app_key": app_key,
                "error": "Could not update MCP URL after authentication",
            }

    except Exception as e:
        logger.error(f"Error refreshing MCP connection for {app_key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug-session/{app_key}")
async def debug_session_uuid(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Debug endpoint to check session UUID consistency"""
    try:
        # Generate session UUID using the same method
        session_uuid = composio_mcp_service._generate_session_uuid(user_id, app_key)

        # Check what's stored in Supabase
        connections = await composio_mcp_service.list_user_mcp_connections(user_id)
        stored_connection = next(
            (conn for conn in connections if conn["app_key"] == app_key), None
        )

        return {
            "user_id": user_id,
            "app_key": app_key,
            "generated_session_uuid": session_uuid,
            "stored_connection": stored_connection,
            "mcp_url_would_be": f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}",
        }

    except Exception as e:
        logger.error(f"Error in debug session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ============================================================================
# NEW: ENHANCED API INTEGRATION ENDPOINTS
# ============================================================================

@router.post("/create-api-connection", response_model=CreateApiConnectionResponse)
async def create_composio_api_connection(
    request: CreateApiConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Create a Composio connection using Direct API (enhanced tool access)
    
    This endpoint provides access to 20+ tools per app vs 13 MCP tools.
    Features better reliability, richer tool metadata, and enhanced functionality.
    
    Args:
        request: Contains app_key and configuration options
        user_id: Current user ID from JWT token
        
    Returns:
        Response with connected account ID and auth URL
    """
    try:
        logger.info(f"Creating API connection for user {user_id}, app {request.app_key}")
        
        # Create API connection via enhanced service
        connection = await composio_mcp_service.create_api_connection(
            user_id=user_id,
            app_key=request.app_key,
            redirect_url=request.redirect_url
        )
        
        if connection.success:
            return CreateApiConnectionResponse(
                success=True,
                app_key=connection.app_key,
                connection_type="api",
                connected_account_id=connection.session_uuid,  # This is connected account ID for API
                auth_url=connection.auth_url,
                message=f"API connection created for {request.app_key}. Enhanced tool access enabled.",
                enhanced_features=True
            )
        else:
            return CreateApiConnectionResponse(
                success=False,
                app_key=connection.app_key,
                connection_type="api",
                error=connection.error,
                message=f"Failed to create API connection for {request.app_key}"
            )
            
    except Exception as e:
        logger.error(f"Error in create_composio_api_connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover-tools-enhanced", response_model=DiscoverToolsEnhancedResponse)
async def discover_tools_enhanced(
    request: DiscoverToolsEnhancedRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Enhanced tool discovery with API vs MCP comparison
    
    This endpoint can discover tools using either:
    - Composio Direct API (20+ tools, enhanced features)
    - Legacy MCP protocol (13 tools, basic features)
    - Automatic selection based on availability
    
    Args:
        request: Contains app_key and discovery options
        user_id: Current user ID from JWT token
        
    Returns:
        Enhanced response with tool count comparison and method used
    """
    try:
        logger.info(f"Enhanced tool discovery for user {user_id}, app {request.app_key}")
        
        # Use enhanced discovery service
        result = await composio_mcp_service.discover_tools_enhanced(
            app_key=request.app_key,
            use_api=request.use_api
        )
        
        if result["success"]:
            # Create comparison data if API is available
            comparison = None
            if composio_mcp_service.api_client and result["method"] == "api":
                # Get MCP count for comparison
                try:
                    mcp_result = await composio_mcp_service._discover_tools_via_mcp(request.app_key)
                    if mcp_result["success"]:
                        comparison = {
                            "api_tools": result["count"],
                            "mcp_tools": mcp_result["count"],
                            "enhancement": result["count"] - mcp_result["count"],
                            "improvement_percentage": round(
                                ((result["count"] - mcp_result["count"]) / mcp_result["count"]) * 100, 1
                            ) if mcp_result["count"] > 0 else 0
                        }
                except Exception as e:
                    logger.warning(f"Could not create comparison data: {e}")
            
            return DiscoverToolsEnhancedResponse(
                success=True,
                app_key=request.app_key,
                tools=result["tools"],
                count=result["count"],
                method=result["method"],
                enhanced_access=(result["method"] == "api"),
                comparison=comparison
            )
        else:
            return DiscoverToolsEnhancedResponse(
                success=False,
                app_key=request.app_key,
                tools=[],
                count=0,
                method=result.get("method", "unknown"),
                error=result.get("error", "Discovery failed")
            )
            
    except Exception as e:
        logger.error(f"Error in discover_tools_enhanced: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/store-api-connection", response_model=StoreApiConnectionResponse)
async def store_api_connection(
    request: StoreApiConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Store API connection with selected tools in user's default agent
    
    This completes the API integration flow by storing the connected account
    and enabling selected tools for agent execution.
    
    Args:
        request: Contains connected account ID and selected tools
        user_id: Current user ID from JWT token
        
    Returns:
        Response confirming storage and tool enablement
    """
    try:
        logger.info(f"Storing API connection for user {user_id}, app {request.app_key}")
        
        # Store API connection via enhanced service
        success = await composio_mcp_service.store_api_connection(
            user_id=user_id,
            app_key=request.app_key,
            connected_account_id=request.connected_account_id,
            selected_tools=request.selected_tools
        )
        
        if success:
            return StoreApiConnectionResponse(
                success=True,
                app_key=request.app_key,
                tools_enabled=len(request.selected_tools),
                message=f"API connection stored for {request.app_key} with {len(request.selected_tools)} tools enabled."
            )
        else:
            return StoreApiConnectionResponse(
                success=False,
                app_key=request.app_key,
                tools_enabled=0,
                message=f"Failed to store API connection for {request.app_key}",
                error="Storage operation failed"
            )
            
    except Exception as e:
        logger.error(f"Error in store_api_connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compare-methods/{app_key}")
async def compare_integration_methods(
    app_key: str,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Compare API vs MCP integration methods for an app
    
    This endpoint helps users understand the benefits of API integration
    by showing tool count differences and feature comparisons.
    
    Args:
        app_key: App to compare methods for
        user_id: Current user ID from JWT token
        
    Returns:
        Detailed comparison between API and MCP methods
    """
    try:
        logger.info(f"Comparing integration methods for {app_key}")
        
        comparison_data = {
            "app_key": app_key,
            "methods": {}
        }
        
        # Test API method if available
        if composio_mcp_service.api_client:
            try:
                api_result = await composio_mcp_service._discover_tools_via_api(app_key)
                comparison_data["methods"]["api"] = {
                    "available": True,
                    "tool_count": api_result.get("count", 0),
                    "success": api_result.get("success", False),
                    "features": [
                        "Enhanced tool access (20+ tools)",
                        "Direct API reliability",
                        "Richer tool metadata",
                        "Better error handling",
                        "Future-proof integration"
                    ],
                    "error": api_result.get("error")
                }
            except Exception as e:
                comparison_data["methods"]["api"] = {
                    "available": False,
                    "error": str(e)
                }
        else:
            comparison_data["methods"]["api"] = {
                "available": False,
                "error": "API client not configured"
            }
        
        # Test MCP method
        try:
            mcp_result = await composio_mcp_service._discover_tools_via_mcp(app_key)
            comparison_data["methods"]["mcp"] = {
                "available": True,
                "tool_count": mcp_result.get("count", 0),
                "success": mcp_result.get("success", False),
                "features": [
                    "Legacy MCP protocol",
                    "Basic tool access (13 tools)",
                    "Existing integration",
                    "Backward compatibility"
                ],
                "error": mcp_result.get("error")
            }
        except Exception as e:
            comparison_data["methods"]["mcp"] = {
                "available": False,
                "error": str(e)
            }
        
        # Calculate recommendation
        api_tools = comparison_data["methods"].get("api", {}).get("tool_count", 0)
        mcp_tools = comparison_data["methods"].get("mcp", {}).get("tool_count", 0)
        
        if api_tools > mcp_tools:
            comparison_data["recommendation"] = {
                "method": "api",
                "reason": f"API provides {api_tools - mcp_tools} more tools ({api_tools} vs {mcp_tools})",
                "improvement": round(((api_tools - mcp_tools) / mcp_tools) * 100, 1) if mcp_tools > 0 else 0
            }
        else:
            comparison_data["recommendation"] = {
                "method": "mcp",
                "reason": "MCP integration is sufficient for this app"
            }
        
        return comparison_data
        
    except Exception as e:
        logger.error(f"Error comparing integration methods: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ============================================================================
# MIGRATION AND COMPATIBILITY ENDPOINTS
# ============================================================================

class MigrateMcpToApiRequest(BaseModel):
    """Request model for migrating MCP to API connection"""
    
    app_key: str
    preserve_tools: bool = True


class MigrationResponse(BaseModel):
    """Response model for migration operations"""
    
    success: bool
    app_key: str
    migrated_tools: int = 0
    api_connection_id: Optional[str] = None
    auth_url: Optional[str] = None
    enhanced_access: bool = False
    message: str
    error: Optional[str] = None


class ConnectionStatusResponse(BaseModel):
    """Response model for connection status"""
    
    success: bool
    app_key: str
    has_mcp: bool
    has_api: bool
    connections: Dict[str, Any]
    recommendation: Optional[str] = None
    reason: Optional[str] = None
    status: str


class CleanupResponse(BaseModel):
    """Response model for cleanup operations"""
    
    success: bool
    removed_connections: int = 0
    app_key: Optional[str] = None
    message: str
    error: Optional[str] = None


@router.post("/migrate-to-api", response_model=MigrationResponse)
async def migrate_mcp_to_api(
    request: MigrateMcpToApiRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Migrate existing MCP connection to API format for enhanced tool access
    
    This endpoint allows users to upgrade from limited MCP tools (13) to 
    full API access (20+ tools) while preserving their tool selections.
    
    Args:
        request: Migration configuration
        user_id: Current user ID from JWT token
        
    Returns:
        Migration result with connection details
    """
    try:
        logger.info(f"Migration request for user {user_id}, app {request.app_key}")
        
        # Perform migration via service
        result = await composio_mcp_service.migrate_mcp_to_api_connection(
            user_id=user_id,
            app_key=request.app_key,
            preserve_tools=request.preserve_tools
        )
        
        if result["success"]:
            return MigrationResponse(
                success=True,
                app_key=result["app_key"],
                migrated_tools=result.get("migrated_tools", 0),
                api_connection_id=result.get("api_connection_id"),
                auth_url=result.get("auth_url"),
                enhanced_access=result.get("enhanced_access", True),
                message=result.get("message", "Migration completed successfully")
            )
        else:
            return MigrationResponse(
                success=False,
                app_key=result["app_key"],
                message="Migration failed",
                error=result.get("error", "Unknown error")
            )
            
    except Exception as e:
        logger.error(f"Error in migrate_mcp_to_api: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection-status/{app_key}", response_model=ConnectionStatusResponse)
async def get_connection_status(
    app_key: str,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Get detailed connection status for an app
    
    Returns information about both MCP and API connections,
    along with migration recommendations.
    
    Args:
        app_key: App to check status for
        user_id: Current user ID from JWT token
        
    Returns:
        Detailed connection status and recommendations
    """
    try:
        logger.info(f"Connection status request for user {user_id}, app {app_key}")
        
        # Get status via service
        status = await composio_mcp_service.get_connection_status(
            user_id=user_id,
            app_key=app_key
        )
        
        return ConnectionStatusResponse(
            success=True,
            app_key=status["app_key"],
            has_mcp=status["has_mcp"],
            has_api=status["has_api"],
            connections=status.get("connections", {}),
            recommendation=status.get("recommendation"),
            reason=status.get("reason"),
            status=status["status"]
        )
        
    except Exception as e:
        logger.error(f"Error in get_connection_status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup-legacy", response_model=CleanupResponse)
async def cleanup_legacy_connections(
    app_key: Optional[str] = None,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Clean up legacy MCP connections that have been migrated to API
    
    This removes old MCP connections that are no longer needed after
    successful migration to API connections.
    
    Args:
        app_key: Specific app to clean up, or None for all apps
        user_id: Current user ID from JWT token
        
    Returns:
        Cleanup result summary
    """
    try:
        logger.info(f"Cleanup request for user {user_id}, app {app_key or 'all'}")
        
        # Perform cleanup via service
        result = await composio_mcp_service.cleanup_legacy_connections(
            user_id=user_id,
            app_key=app_key
        )
        
        return CleanupResponse(
            success=result["success"],
            removed_connections=result.get("removed_connections", 0),
            app_key=result.get("app_key"),
            message=result.get("message", "Cleanup completed"),
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error in cleanup_legacy_connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check for Composio integration (both MCP and API)"""
    health_data = {
        "status": "healthy",
        "service": "composio_integration",
        "version": "2.0.0",  # Updated version for enhanced features
        "features": {
            "mcp_integration": True,
            "api_integration": composio_mcp_service.api_client is not None,
            "enhanced_tool_access": composio_mcp_service.enable_api_integration,
            "migration_support": True  # NEW: Migration capabilities
        }
    }
    
    # Check API client health if available
    if composio_mcp_service.api_client:
        try:
            async with composio_mcp_service.api_client as client:
                api_health = await client.health_check()
                health_data["api_status"] = api_health
        except Exception as e:
            health_data["api_status"] = {
                "healthy": False,
                "error": str(e)
            }
    
    return health_data
