"""
Composio Direct API Client
Provides direct integration with Composio's REST API for enhanced tool access

This client handles:
- Authentication and connected account management
- Tool discovery (20+ tools vs 13 MCP tools)
- Direct tool execution via HTTP API
- Rate limiting and error handling
- OAuth flow management
"""

import os
import asyncio
import httpx
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import uuid
from utils.logger import logger


@dataclass
class ApiTool:
    """Represents a tool available via Composio API"""
    name: str
    description: str
    parameters: Dict[str, Any]
    app_key: str
    original_name: str  # Original Composio action name
    auth_required: bool = True
    execution_type: str = "composio_api"


@dataclass
class ConnectedAccount:
    """Represents an authenticated user connection to a service"""
    id: str
    app_key: str
    user_id: str
    status: str  # "active", "expired", "revoked", "pending"
    auth_data: Dict[str, Any]
    created_at: str
    expires_at: Optional[str] = None
    redirect_url: Optional[str] = None


class ComposioApiError(Exception):
    """Custom exception for Composio API errors"""
    def __init__(self, message: str, status_code: int = None, response_data: Dict = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(self.message)


class ComposioApiClient:
    """
    Core client for Composio Direct API integration
    
    Provides comprehensive access to Composio's full API capabilities:
    - 20+ tools per app vs 13 MCP tools
    - Direct HTTP API execution
    - Enhanced authentication management
    - Better error handling and reliability
    """
    
    def __init__(self):
        self.base_url = "https://backend.composio.dev/api/v1"
        self.mcp_base_url = "https://mcp.composio.dev"
        self.api_key = os.getenv("COMPOSIO_API_KEY")
        
        if not self.api_key:
            logger.warning("COMPOSIO_API_KEY not found in environment. API functionality will be limited.")
        
        # Request settings
        self.timeout = httpx.Timeout(30.0)
        self.limits = httpx.Limits(max_keepalive_connections=20, max_connections=100)
        
        # Rate limiting (basic implementation)
        self._rate_limit_cache = {}
        self.max_requests_per_minute = 100
        
        # Session management
        self._session: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self._session = httpx.AsyncClient(
            timeout=self.timeout,
            limits=self.limits,
            headers=self._get_default_headers()
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self._session:
            await self._session.aclose()
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "Atlas-Agents/1.0"
        }
        
        if self.api_key:
            headers["x-api-key"] = self.api_key
            
        return headers
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> httpx.Response:
        """
        Make authenticated API request with error handling
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (will be appended to base_url)
            **kwargs: Additional arguments for httpx request
            
        Returns:
            httpx.Response object
            
        Raises:
            ComposioApiError: For API-specific errors
        """
        if not self._session:
            raise ComposioApiError("Client session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            # Apply rate limiting check
            await self._check_rate_limit()
            
            # Make request
            response = await self._session.request(method, url, **kwargs)
            
            # Handle common HTTP errors
            if response.status_code == 401:
                raise ComposioApiError(
                    "Authentication failed. Check your API key.",
                    status_code=401,
                    response_data=self._safe_json(response)
                )
            elif response.status_code == 403:
                raise ComposioApiError(
                    "Insufficient permissions for this operation.",
                    status_code=403,
                    response_data=self._safe_json(response)
                )
            elif response.status_code == 429:
                raise ComposioApiError(
                    "Rate limit exceeded. Please wait before retrying.",
                    status_code=429,
                    response_data=self._safe_json(response)
                )
            elif response.status_code >= 500:
                raise ComposioApiError(
                    "Composio service temporarily unavailable.",
                    status_code=response.status_code,
                    response_data=self._safe_json(response)
                )
            
            return response
            
        except httpx.TimeoutException:
            raise ComposioApiError("Request timeout. Please try again.")
        except httpx.RequestError as e:
            raise ComposioApiError(f"Network error: {str(e)}")
    
    def _safe_json(self, response: httpx.Response) -> Dict[str, Any]:
        """Safely parse JSON response"""
        try:
            return response.json()
        except:
            return {"raw_response": response.text}
    
    async def _check_rate_limit(self):
        """Basic rate limiting implementation"""
        now = datetime.now()
        
        # Clean old entries
        cutoff = now - timedelta(minutes=1)
        self._rate_limit_cache = {
            k: v for k, v in self._rate_limit_cache.items() 
            if v > cutoff
        }
        
        # Check if we're at the limit
        if len(self._rate_limit_cache) >= self.max_requests_per_minute:
            sleep_time = 60 - (now - min(self._rate_limit_cache.values())).seconds
            logger.warning(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
            await asyncio.sleep(sleep_time)
        
        # Add current request
        request_id = str(uuid.uuid4())
        self._rate_limit_cache[request_id] = now
    
    # ============================================================================
    # CONNECTED ACCOUNT MANAGEMENT
    # ============================================================================
    
    async def create_connected_account(
        self,
        app_key: str,
        user_id: str,
        redirect_url: str = None
    ) -> ConnectedAccount:
        """
        Create a new connected account for OAuth authentication
        
        Args:
            app_key: The app to connect (e.g., "gmail", "notion")
            user_id: Atlas user ID
            redirect_url: URL to redirect after OAuth completion
            
        Returns:
            ConnectedAccount object with OAuth redirect URL
        """
        try:
            # Use entity_id as user identifier for Composio
            entity_id = f"atlas_user_{user_id}"
            
            payload = {
                "integrationId": app_key,
                "entityId": entity_id,
            }
            
            if redirect_url:
                payload["redirectUrl"] = redirect_url
            
            logger.info(f"Creating connected account for {app_key}, user {user_id}")
            
            response = await self._make_request(
                "POST",
                "connected-accounts",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                
                return ConnectedAccount(
                    id=data.get("connectedAccountId", f"temp_{uuid.uuid4()}"),
                    app_key=app_key,
                    user_id=user_id,
                    status=data.get("connectionStatus", "pending").lower(),
                    auth_data=data,
                    created_at=datetime.now().isoformat(),
                    redirect_url=data.get("redirectUrl")
                )
            else:
                raise ComposioApiError(
                    f"Failed to create connected account: {response.status_code}",
                    status_code=response.status_code,
                    response_data=self._safe_json(response)
                )
                
        except ComposioApiError:
            raise
        except Exception as e:
            logger.error(f"Error creating connected account: {e}")
            raise ComposioApiError(f"Failed to create connected account: {str(e)}")
    
    async def get_connected_accounts(self, user_id: str = None) -> List[ConnectedAccount]:
        """
        Get list of connected accounts
        
        Args:
            user_id: Optional user ID to filter accounts
            
        Returns:
            List of ConnectedAccount objects
        """
        try:
            params = {}
            if user_id:
                params["entityId"] = f"atlas_user_{user_id}"
            
            response = await self._make_request(
                "GET",
                "connected-accounts",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                accounts = []
                
                for account_data in data.get("items", []):
                    accounts.append(ConnectedAccount(
                        id=account_data.get("id", ""),
                        app_key=account_data.get("integrationId", ""),
                        user_id=user_id or account_data.get("entityId", "").replace("atlas_user_", ""),
                        status=account_data.get("status", "unknown").lower(),
                        auth_data=account_data,
                        created_at=account_data.get("createdAt", ""),
                        expires_at=account_data.get("expiresAt")
                    ))
                
                return accounts
            else:
                logger.warning(f"Failed to get connected accounts: {response.status_code}")
                return []
                
        except ComposioApiError:
            raise
        except Exception as e:
            logger.error(f"Error getting connected accounts: {e}")
            return []
    
    async def verify_connection_status(self, connected_account_id: str) -> Dict[str, Any]:
        """
        Verify if a connected account is still valid
        
        Args:
            connected_account_id: ID of the connected account
            
        Returns:
            Dict with status information
        """
        try:
            response = await self._make_request(
                "GET",
                f"connected-accounts/{connected_account_id}"
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "valid": True,
                    "status": data.get("status", "unknown"),
                    "last_used": data.get("lastUsed"),
                    "expires_at": data.get("expiresAt")
                }
            elif response.status_code == 404:
                return {
                    "valid": False,
                    "status": "not_found",
                    "error": "Connected account not found"
                }
            else:
                return {
                    "valid": False,
                    "status": "error",
                    "error": f"Verification failed: {response.status_code}"
                }
                
        except ComposioApiError as e:
            return {
                "valid": False,
                "status": "error",
                "error": str(e)
            }
        except Exception as e:
            logger.error(f"Error verifying connection: {e}")
            return {
                "valid": False,
                "status": "error",
                "error": str(e)
            }
    
    async def delete_connected_account(self, connected_account_id: str) -> bool:
        """
        Delete a connected account
        
        Args:
            connected_account_id: ID of the account to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            response = await self._make_request(
                "DELETE",
                f"connected-accounts/{connected_account_id}"
            )
            
            return response.status_code in [200, 204]
            
        except ComposioApiError:
            return False
        except Exception as e:
            logger.error(f"Error deleting connected account: {e}")
            return False
    
    # ============================================================================
    # TOOL DISCOVERY
    # ============================================================================
    
    async def discover_tools(self, app_key: str) -> List[ApiTool]:
        """
        Discover all available tools for an app via API
        This returns the FULL tool set (20+) vs MCP's limited 13 tools
        
        Args:
            app_key: App to discover tools for (e.g., "gmail", "notion")
            
        Returns:
            List of ApiTool objects
        """
        try:
            # Use the MCP API endpoint to get app metadata
            # This provides access to the full tool catalog
            url = f"{self.mcp_base_url}/api/apps/{app_key}"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    app_data = response.json()
                    actions = app_data.get("actions", [])
                    
                    logger.info(f"Discovered {len(actions)} tools for {app_key} via API")
                    
                    tools = []
                    for action in actions:
                        # Convert Composio action to our ApiTool format
                        tool = ApiTool(
                            name=f"{app_key.upper()}_{action['name'].upper().replace(' ', '_').replace('-', '_')}",
                            description=action.get("description", ""),
                            parameters=self._extract_parameters(action),
                            app_key=app_key,
                            original_name=action["name"],
                            auth_required=True,
                            execution_type="composio_api"
                        )
                        tools.append(tool)
                    
                    return tools
                else:
                    logger.warning(f"Failed to discover tools for {app_key}: {response.status_code}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error discovering tools for {app_key}: {e}")
            return []
    
    def _extract_parameters(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and format parameters from Composio action
        
        Args:
            action: Composio action definition
            
        Returns:
            OpenAPI-compatible parameter schema
        """
        # Basic parameter extraction
        # In a real implementation, this would parse the full action schema
        parameters = {
            "type": "object",
            "properties": {
                "entityId": {
                    "type": "string",
                    "description": "Connected account identifier"
                }
            },
            "required": ["entityId"]
        }
        
        # Add any specific parameters from the action definition
        if "inputSchema" in action:
            schema = action["inputSchema"]
            if isinstance(schema, dict) and "properties" in schema:
                parameters["properties"].update(schema["properties"])
                if "required" in schema:
                    parameters["required"].extend(schema["required"])
        
        return parameters
    
    async def get_tool_schema(self, app_key: str, tool_name: str) -> Dict[str, Any]:
        """
        Get detailed OpenAPI schema for a specific tool
        
        Args:
            app_key: App key
            tool_name: Tool name
            
        Returns:
            OpenAPI schema for the tool
        """
        # This would fetch detailed schema from Composio
        # For now, return basic schema
        return {
            "type": "object",
            "properties": {
                "entityId": {
                    "type": "string",
                    "description": "Connected account identifier"
                }
            },
            "required": ["entityId"]
        }
    
    # ============================================================================
    # TOOL EXECUTION
    # ============================================================================
    
    async def execute_tool(
        self,
        connected_account_id: str,
        tool_name: str,
        parameters: Dict[str, Any],
        app_key: str,
        original_action_name: str = None
    ) -> Dict[str, Any]:
        """
        Execute a tool via Composio Direct API
        
        Args:
            connected_account_id: ID of authenticated connection
            tool_name: Name of tool to execute
            parameters: Tool parameters
            app_key: App key
            original_action_name: Original Composio action name
            
        Returns:
            Tool execution result
        """
        try:
            # Use original action name if provided, otherwise derive it
            action_name = original_action_name or self._derive_action_name(tool_name, app_key)
            
            # Prepare execution payload
            payload = {
                "connectedAccountId": connected_account_id,
                "entityId": f"atlas_user_{parameters.get('user_id', 'unknown')}",
                "input": {k: v for k, v in parameters.items() if k != 'user_id'}
            }
            
            logger.info(f"Executing tool {tool_name} (action: {action_name}) for account {connected_account_id}")
            
            # Execute via Composio API
            response = await self._make_request(
                "POST",
                f"actions/{action_name}/execute",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                
                return {
                    "success": True,
                    "output": result.get("data", result),
                    "execution_id": result.get("executionId"),
                    "metadata": {
                        "tool_name": tool_name,
                        "action_name": action_name,
                        "app_key": app_key,
                        "execution_method": "composio_api"
                    }
                }
            else:
                error_data = self._safe_json(response)
                logger.error(f"Tool execution failed: {response.status_code} - {error_data}")
                
                return {
                    "success": False,
                    "error": f"Execution failed: {response.status_code}",
                    "details": error_data,
                    "metadata": {
                        "tool_name": tool_name,
                        "action_name": action_name,
                        "app_key": app_key
                    }
                }
                
        except ComposioApiError as e:
            logger.error(f"API error executing tool {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "status_code": e.status_code,
                "metadata": {
                    "tool_name": tool_name,
                    "app_key": app_key
                }
            }
        except Exception as e:
            logger.error(f"Unexpected error executing tool {tool_name}: {e}")
            return {
                "success": False,
                "error": f"Execution failed: {str(e)}",
                "metadata": {
                    "tool_name": tool_name,
                    "app_key": app_key
                }
            }
    
    def _derive_action_name(self, tool_name: str, app_key: str) -> str:
        """
        Derive original Composio action name from our tool name
        
        Args:
            tool_name: Our formatted tool name (e.g., "GMAIL_SEND_EMAIL")
            app_key: App key (e.g., "gmail")
            
        Returns:
            Original Composio action name
        """
        # Remove app prefix and convert to title case
        if tool_name.upper().startswith(app_key.upper() + "_"):
            action_part = tool_name[len(app_key) + 1:]
            return action_part.replace("_", " ").title()
        
        return tool_name.replace("_", " ").title()
    
    # ============================================================================
    # UTILITY METHODS
    # ============================================================================
    
    async def get_supported_apps(self) -> List[Dict[str, Any]]:
        """
        Get list of all supported apps from Composio
        
        Returns:
            List of app definitions
        """
        try:
            url = f"{self.mcp_base_url}/api/apps"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    apps = response.json()
                    logger.info(f"Retrieved {len(apps)} supported apps")
                    return apps
                else:
                    logger.warning(f"Failed to get supported apps: {response.status_code}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting supported apps: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check API health and connectivity
        
        Returns:
            Health status information
        """
        try:
            # Simple endpoint to test connectivity
            response = await self._make_request("GET", "health")
            
            return {
                "healthy": response.status_code == 200,
                "status_code": response.status_code,
                "api_key_configured": bool(self.api_key),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "api_key_configured": bool(self.api_key),
                "timestamp": datetime.now().isoformat()
            }


# Global instance for easy import
composio_api_client = ComposioApiClient()