"""
Comprehensive Test Suite for Composio API Integration

Tests the complete migration from MCP to Direct API integration,
ensuring all functionality works correctly across the full stack.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

# Import the modules under test
from services.composio_api_client import ComposioApiClient, ApiTool, ConnectedAccount, ComposioApiError
from services.composio_integration import ComposioMCPService, ComposioMCPConnection
from agent.tools.mcp_tool_wrapper import MCPToolWrapper
from api.composio_mcp import (
    create_composio_api_connection,
    discover_tools_enhanced,
    store_api_connection,
    migrate_mcp_to_api,
    get_connection_status,
    cleanup_legacy_connections
)


class TestComposioApiClient:
    """Test suite for the core API client"""
    
    @pytest.fixture
    def api_client(self):
        """Create API client instance for testing"""
        return ComposioApiClient()
    
    @pytest.fixture
    def mock_httpx_response(self):
        """Mock httpx response for API calls"""
        response = Mock()
        response.status_code = 200
        response.json.return_value = {
            "connectedAccountId": "conn_test_123",
            "connectionStatus": "active",
            "redirectUrl": "https://oauth.composio.dev/auth?token=test"
        }
        return response
    
    @pytest.mark.asyncio
    async def test_create_connected_account_success(self, api_client):
        """Test successful connected account creation"""
        with patch.object(api_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "connectedAccountId": "conn_gmail_123",
                "connectionStatus": "pending",
                "redirectUrl": "https://oauth.composio.dev/auth?app=gmail"
            }
            mock_request.return_value = mock_response
            
            async with api_client as client:
                result = await client.create_connected_account(
                    app_key="gmail",
                    user_id="user_123",
                    redirect_url="https://app.com/callback"
                )
            
            assert result.id == "conn_gmail_123"
            assert result.app_key == "gmail"
            assert result.user_id == "user_123"
            assert result.status == "pending"
            assert "oauth.composio.dev" in result.redirect_url
    
    @pytest.mark.asyncio
    async def test_discover_tools_enhanced_access(self, api_client):
        """Test tool discovery returns enhanced tool set (20+ vs 13)"""
        mock_app_data = {
            "actions": [
                {"name": "send_email", "description": "Send an email"},
                {"name": "create_draft", "description": "Create email draft"},
                {"name": "schedule_email", "description": "Schedule email delivery"},
                {"name": "bulk_operations", "description": "Bulk email operations"},
                {"name": "advanced_search", "description": "Advanced email search"},
                # ... more actions to simulate 20+ tools
            ] + [{"name": f"tool_{i}", "description": f"Tool {i}"} for i in range(16, 25)]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_app_data
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            tools = await api_client.discover_tools("gmail")
            
            # Verify we get enhanced tool access
            assert len(tools) >= 20, f"Expected 20+ tools, got {len(tools)}"
            
            # Verify tool format
            assert all(isinstance(tool, ApiTool) for tool in tools)
            assert all(tool.app_key == "gmail" for tool in tools)
            assert all(tool.execution_type == "composio_api" for tool in tools)
    
    @pytest.mark.asyncio
    async def test_execute_tool_success(self, api_client):
        """Test successful tool execution via API"""
        with patch.object(api_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "data": {"message_id": "msg_123", "status": "sent"},
                "executionId": "exec_456"
            }
            mock_request.return_value = mock_response
            
            async with api_client as client:
                result = await client.execute_tool(
                    connected_account_id="conn_123",
                    tool_name="GMAIL_SEND_EMAIL",
                    parameters={"to": "<EMAIL>", "subject": "Test"},
                    app_key="gmail",
                    original_action_name="send_email"
                )
            
            assert result["success"] is True
            assert result["execution_id"] == "exec_456"
            assert result["output"]["message_id"] == "msg_123"
            assert result["metadata"]["execution_method"] == "composio_api"
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, api_client):
        """Test proper error handling for API failures"""
        with patch.object(api_client, '_make_request', new_callable=AsyncMock) as mock_request:
            # Test 401 authentication error
            mock_request.side_effect = ComposioApiError(
                "Authentication failed. Check your API key.",
                status_code=401
            )
            
            async with api_client as client:
                with pytest.raises(ComposioApiError) as exc_info:
                    await client.create_connected_account("gmail", "user_123")
                
                assert exc_info.value.status_code == 401
                assert "Authentication failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, api_client):
        """Test rate limiting functionality"""
        # Mock multiple rapid requests
        with patch.object(api_client, '_check_rate_limit', new_callable=AsyncMock) as mock_rate_limit:
            mock_rate_limit.return_value = None  # No rate limit hit
            
            async with api_client as client:
                # Should not raise rate limit exception
                await client._check_rate_limit()
                
                # Simulate rate limit hit
                mock_rate_limit.side_effect = ComposioApiError(
                    "Rate limit exceeded", status_code=429
                )
                
                with pytest.raises(ComposioApiError) as exc_info:
                    await client._check_rate_limit()
                
                assert exc_info.value.status_code == 429


class TestComposioIntegrationService:
    """Test suite for the integration service"""
    
    @pytest.fixture
    def service(self):
        """Create service instance for testing"""
        service = ComposioMCPService()
        # Mock the API client to avoid real API calls
        service.api_client = AsyncMock()
        return service
    
    @pytest.mark.asyncio
    async def test_create_api_connection(self, service):
        """Test API connection creation"""
        # Mock API client response
        mock_connected_account = ConnectedAccount(
            id="conn_123",
            app_key="gmail",
            user_id="user_123",
            status="active",
            auth_data={},
            created_at="2024-01-01T00:00:00Z",
            redirect_url="https://oauth.composio.dev/auth"
        )
        
        service.api_client.create_connected_account.return_value = mock_connected_account
        
        result = await service.create_api_connection(
            user_id="user_123",
            app_key="gmail",
            redirect_url="https://app.com/callback"
        )
        
        assert result.success is True
        assert result.app_key == "gmail"
        assert result.session_uuid == "conn_123"
        assert result.qualified_name == "composio_api/gmail"
        assert "oauth.composio.dev" in result.auth_url
    
    @pytest.mark.asyncio
    async def test_discover_tools_enhanced_api_vs_mcp(self, service):
        """Test enhanced tool discovery comparing API vs MCP"""
        # Mock API tool discovery (20+ tools)
        api_tools = [ApiTool(
            name=f"GMAIL_TOOL_{i}",
            description=f"Gmail tool {i}",
            parameters={},
            app_key="gmail",
            original_name=f"tool_{i}"
        ) for i in range(25)]  # 25 tools via API
        
        service.api_client.discover_tools.return_value = api_tools
        
        # Mock MCP discovery for comparison (13 tools)
        with patch.object(service, '_discover_tools_via_mcp', new_callable=AsyncMock) as mock_mcp:
            mock_mcp.return_value = {
                "success": True,
                "count": 13,
                "tools": [{"name": f"mcp_tool_{i}"} for i in range(13)]
            }
            
            result = await service.discover_tools_enhanced("gmail", use_api=True)
            
            assert result["success"] is True
            assert result["count"] == 25  # Enhanced API access
            assert result["method"] == "api"
            
            # Should have comparison data
            assert "comparison" in result or len(result["tools"]) > 13
    
    @pytest.mark.asyncio
    async def test_migration_mcp_to_api(self, service):
        """Test complete migration from MCP to API"""
        # Mock existing MCP connection
        with patch.object(service, '_find_mcp_connection', new_callable=AsyncMock) as mock_find:
            mock_find.return_value = {
                "type": "http",
                "name": "Gmail",
                "config": {"url": "https://mcp.composio.dev/partner/composio/gmail/mcp"},
                "enabledTools": ["GMAIL_SEND_EMAIL", "GMAIL_READ_EMAIL"]
            }
            
            # Mock API connection creation
            service.api_client.create_connected_account.return_value = ConnectedAccount(
                id="conn_api_123",
                app_key="gmail",
                user_id="user_123",
                status="active",
                auth_data={},
                created_at="2024-01-01T00:00:00Z",
                redirect_url="https://oauth.composio.dev/auth"
            )
            
            # Mock storage operations
            with patch.object(service, 'store_api_connection', new_callable=AsyncMock) as mock_store:
                mock_store.return_value = True
                
                with patch.object(service, '_mark_mcp_as_migrated', new_callable=AsyncMock) as mock_mark:
                    mock_mark.return_value = True
                    
                    result = await service.migrate_mcp_to_api_connection(
                        user_id="user_123",
                        app_key="gmail",
                        preserve_tools=True
                    )
                    
                    assert result["success"] is True
                    assert result["app_key"] == "gmail"
                    assert result["migrated_tools"] == 2  # Preserved 2 tools
                    assert result["enhanced_access"] is True
                    assert "conn_api_123" in result["api_connection_id"]
    
    @pytest.mark.asyncio
    async def test_connection_status_analysis(self, service):
        """Test connection status analysis and recommendations"""
        # Mock Supabase queries for agent and connections
        with patch.object(service.supabase, 'schema') as mock_schema:
            # Mock account lookup
            mock_schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [
                {"id": "account_123"}
            ]
            
            # Mock agent lookup with both MCP and API connections
            service.supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [
                {
                    "custom_mcps": [
                        {
                            "type": "http",
                            "name": "Gmail",
                            "config": {"url": "https://mcp.composio.dev/partner/composio/gmail/mcp"},
                            "enabledTools": ["GMAIL_SEND_EMAIL"]
                        },
                        {
                            "type": "composio_api",
                            "name": "Gmail API",
                            "config": {"app_key": "gmail", "connected_account_id": "conn_123"},
                            "enabledTools": ["GMAIL_SEND_EMAIL", "GMAIL_CREATE_DRAFT", "GMAIL_SCHEDULE_EMAIL"],
                            "metadata": {"enhanced_access": True}
                        }
                    ]
                }
            ]
            
            result = await service.get_connection_status("user_123", "gmail")
            
            assert result["app_key"] == "gmail"
            assert result["has_mcp"] is True
            assert result["has_api"] is True
            assert result["recommendation"] == "use_api"
            assert "better tool access" in result["reason"]
            
            # Check connection details
            assert "mcp" in result["connections"]
            assert "api" in result["connections"]
            assert result["connections"]["api"]["enabled_tools"] == 3  # More tools via API


class TestMCPToolWrapper:
    """Test suite for enhanced MCP tool wrapper with API support"""
    
    @pytest.fixture
    def tool_wrapper(self):
        """Create tool wrapper with mixed MCP and API configs"""
        configs = [
            {
                "type": "http",
                "name": "Exa Search",
                "config": {"url": "https://mcp.exa.ai"}
            },
            {
                "type": "composio_api",
                "name": "Gmail API",
                "config": {
                    "app_key": "gmail",
                    "connected_account_id": "conn_123",
                    "user_id": "user_123"
                },
                "enabledTools": ["GMAIL_SEND_EMAIL", "GMAIL_CREATE_DRAFT"]
            }
        ]
        return MCPToolWrapper(mcp_configs=configs)
    
    @pytest.mark.asyncio
    async def test_api_tool_initialization(self, tool_wrapper):
        """Test API tools are properly initialized alongside MCP tools"""
        # Mock API client and discovery
        if tool_wrapper.api_client:
            tool_wrapper.api_client.discover_tools = AsyncMock(return_value=[
                ApiTool(
                    name="GMAIL_SEND_EMAIL",
                    description="Send an email via API",
                    parameters={"type": "object", "properties": {}},
                    app_key="gmail",
                    original_name="send_email"
                ),
                ApiTool(
                    name="GMAIL_CREATE_DRAFT",
                    description="Create email draft via API",
                    parameters={"type": "object", "properties": {}},
                    app_key="gmail",
                    original_name="create_draft"
                )
            ])
            
            await tool_wrapper._ensure_initialized()
            
            # Check that API tools are registered
            assert len(tool_wrapper._api_tools) == 2
            assert "GMAIL_SEND_EMAIL" in tool_wrapper._api_tools
            assert "GMAIL_CREATE_DRAFT" in tool_wrapper._api_tools
            
            # Check tool metadata
            gmail_tool = tool_wrapper._api_tools["GMAIL_SEND_EMAIL"]
            assert gmail_tool["execution_type"] == "composio_api"
            assert gmail_tool["app_key"] == "gmail"
            assert gmail_tool["connected_account_id"] == "conn_123"
    
    @pytest.mark.asyncio
    async def test_api_tool_execution_routing(self, tool_wrapper):
        """Test that API tools are executed via API client, not MCP"""
        if tool_wrapper.api_client:
            # Mock successful API execution
            tool_wrapper.api_client.execute_tool = AsyncMock(return_value={
                "success": True,
                "output": {"message_id": "msg_123"},
                "execution_id": "exec_456"
            })
            
            # Initialize tools
            await tool_wrapper._ensure_initialized()
            
            # Execute API tool
            result = await tool_wrapper._execute_api_tool(
                "GMAIL_SEND_EMAIL",
                {"to": "<EMAIL>", "subject": "Test"}
            )
            
            assert result.success is True
            assert "msg_123" in result.content
            
            # Verify API client was called, not MCP
            tool_wrapper.api_client.execute_tool.assert_called_once()


class TestAPIEndpoints:
    """Test suite for API endpoints"""
    
    @pytest.mark.asyncio
    async def test_migration_endpoint(self):
        """Test migration endpoint functionality"""
        # Mock dependencies
        with patch('api.composio_mcp.composio_mcp_service') as mock_service:
            mock_service.migrate_mcp_to_api_connection.return_value = {
                "success": True,
                "app_key": "gmail",
                "migrated_tools": 3,
                "api_connection_id": "conn_123",
                "auth_url": "https://oauth.composio.dev/auth",
                "enhanced_access": True,
                "message": "Migration completed"
            }
            
            from api.composio_mcp import MigrateMcpToApiRequest
            
            request = MigrateMcpToApiRequest(app_key="gmail", preserve_tools=True)
            result = await migrate_mcp_to_api(request, user_id="user_123")
            
            assert result.success is True
            assert result.app_key == "gmail"
            assert result.migrated_tools == 3
            assert result.enhanced_access is True
    
    @pytest.mark.asyncio
    async def test_connection_status_endpoint(self):
        """Test connection status endpoint"""
        with patch('api.composio_mcp.composio_mcp_service') as mock_service:
            mock_service.get_connection_status.return_value = {
                "app_key": "gmail",
                "has_mcp": True,
                "has_api": True,
                "connections": {
                    "mcp": {"enabled_tools": 2},
                    "api": {"enabled_tools": 5}
                },
                "recommendation": "use_api",
                "reason": "API provides better tool access",
                "status": "analyzed"
            }
            
            result = await get_connection_status("gmail", user_id="user_123")
            
            assert result.success is True
            assert result.app_key == "gmail"
            assert result.has_api is True
            assert result.recommendation == "use_api"


class TestPerformanceAndReliability:
    """Test suite for performance and reliability aspects"""
    
    @pytest.mark.asyncio
    async def test_concurrent_api_calls(self):
        """Test that API client handles concurrent requests properly"""
        api_client = ComposioApiClient()
        
        async def mock_request():
            async with api_client as client:
                return await client._make_request("GET", "test")
        
        with patch.object(api_client, '_session') as mock_session:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_session.request.return_value = mock_response
            
            # Run multiple concurrent requests
            tasks = [mock_request() for _ in range(10)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All should succeed
            assert all(not isinstance(r, Exception) for r in results)
    
    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery and retry mechanisms"""
        service = ComposioMCPService()
        
        # Test graceful degradation when API is unavailable
        service.api_client = None
        
        result = await service.create_api_connection("user_123", "gmail")
        
        assert result.success is False
        assert "API client not available" in result.error
    
    def test_backward_compatibility(self):
        """Test that existing MCP functionality still works"""
        # Ensure old MCP config format still works
        old_config = {
            "type": "http",
            "name": "Gmail",
            "config": {"url": "https://mcp.composio.dev/partner/composio/gmail/mcp"},
            "enabledTools": ["GMAIL_SEND_EMAIL"]
        }
        
        # Should be properly categorized as MCP config
        wrapper = MCPToolWrapper([old_config])
        mcp_configs = [cfg for cfg in wrapper.mcp_configs_filtered if cfg.get('type') != 'composio_api']
        
        assert len(mcp_configs) == 1
        assert mcp_configs[0] == old_config


if __name__ == "__main__":
    # Run the test suite
    pytest.main([__file__, "-v", "--tb=short"])